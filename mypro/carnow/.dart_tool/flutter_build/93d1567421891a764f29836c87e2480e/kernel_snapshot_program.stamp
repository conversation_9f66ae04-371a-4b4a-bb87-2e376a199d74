{"inputs": ["/Users/<USER>/mypro/carnow/.dart_tool/package_config_subset", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/app_links_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_method_channel.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/async.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_cache.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_memoizer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/byte_collector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/cancelable_operation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/chunked_stream_reader.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/event_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/future.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_consumer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_subscription.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/future_group.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/lazy_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/null_stream_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/restartable_timer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/error.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/future.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/value.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/single_subscription_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/sink_base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_closer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_completer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_group.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_queue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_completer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/handler_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/reject_errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/typed.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_splitter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_subscription_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_zip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/subscription_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed/stream_subscription.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed_stream_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "/Users/<USER>/mypro/carnow/lib/core/app/app_initialization.dart", "/Users/<USER>/mypro/carnow/lib/core/app/carnow_app.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "/Users/<USER>/mypro/carnow/lib/core/app/performance_main_thread_fix.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_initialization_service.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_interfaces.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_interfaces.freezed.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_models.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_provider_initializer.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/auth_provider_initializer.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/enhanced_secure_token_storage.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/enhanced_secure_token_storage.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_interface_v7.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_service.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/google_auth_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/google_oauth_config.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/token_storage_migration.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/token_storage_migration.g.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/unified_auth_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/auth/unified_auth_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/config/app_config.dart", "/Users/<USER>/mypro/carnow/lib/core/config/backend_config.dart", "/Users/<USER>/mypro/carnow/lib/core/config/env_config.dart", "/Users/<USER>/mypro/carnow/lib/core/config/local_env_config.dart", "/Users/<USER>/mypro/carnow/lib/core/config/server_urls.dart", "/Users/<USER>/mypro/carnow/lib/core/config/session_persistence_config.dart", "/Users/<USER>/mypro/carnow/lib/core/errors/app_error.dart", "/Users/<USER>/mypro/carnow/lib/core/errors/failure.dart", "/Users/<USER>/mypro/carnow/lib/core/errors/result.dart", "/Users/<USER>/mypro/carnow/lib/core/errors/unified_error_handler.dart", "/Users/<USER>/mypro/carnow/lib/core/errors/unified_error_handler.g.dart", "/Users/<USER>/mypro/carnow/lib/core/extensions/user_seller_extension.dart", "/Users/<USER>/mypro/carnow/lib/core/interceptors/error_interceptor.dart", "/Users/<USER>/mypro/carnow/lib/core/models/api_response.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_transaction.g.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_user.g.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/carnow_wallet.g.dart", "/Users/<USER>/mypro/carnow/lib/core/models/city_model.dart", "/Users/<USER>/mypro/carnow/lib/core/models/enums.dart", "/Users/<USER>/mypro/carnow/lib/core/models/product_model.dart", "/Users/<USER>/mypro/carnow/lib/core/models/products_result_model.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_error.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_error.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_request.g.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.freezed.dart", "/Users/<USER>/mypro/carnow/lib/core/models/subscription_response.g.dart", "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_app_bar.dart", "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_navigation_system.dart", "/Users/<USER>/mypro/carnow/lib/core/navigation/unified_navigation_system.g.dart", "/Users/<USER>/mypro/carnow/lib/core/networking/simple_api_client.dart", "/Users/<USER>/mypro/carnow/lib/core/networking/simple_api_client.g.dart", "/Users/<USER>/mypro/carnow/lib/core/performance/startup_optimizer.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/admin_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/carnow_providers.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/carnow_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/clean_products_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/clean_products_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/dio_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/dio_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/location_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/location_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/simple_product_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/simple_product_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/subscription_flow_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/subscription_flow_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/theme_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/theme_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/users_provider.dart", "/Users/<USER>/mypro/carnow/lib/core/providers/users_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/core/repositories/base_repository.dart", "/Users/<USER>/mypro/carnow/lib/core/repositories/product_repository.dart", "/Users/<USER>/mypro/carnow/lib/core/repositories/taxonomy_repository.dart", "/Users/<USER>/mypro/carnow/lib/core/router/app_router.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/account_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/cart_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/chat_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/developer_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/missing_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/router/routes/seller_routes.dart", "/Users/<USER>/mypro/carnow/lib/core/services/backend_resilience_initializer.dart", "/Users/<USER>/mypro/carnow/lib/core/services/backend_resilience_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/carnow_api_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/location_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/network_connectivity_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/network_connectivity_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/services/notification_api_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/notification_api_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/services/recommendation_api_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/recommendation_api_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/services/subscription_service.dart", "/Users/<USER>/mypro/carnow/lib/core/services/subscription_service.g.dart", "/Users/<USER>/mypro/carnow/lib/core/theme/app_color_extensions.dart", "/Users/<USER>/mypro/carnow/lib/core/theme/app_colors.dart", "/Users/<USER>/mypro/carnow/lib/core/theme/app_theme.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/error_handler.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/formatters.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/image_utils.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/type_helpers.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/ui_utils.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/unified_logger.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/unified_theme_extension.dart", "/Users/<USER>/mypro/carnow/lib/core/utils/validators.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/app_error_widget.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/custom_text_field.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/error_boundary.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/error_display.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/loading_indicator.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/loading_indicators.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/unified_app_bar.dart", "/Users/<USER>/mypro/carnow/lib/core/widgets/unified_card.dart", "/Users/<USER>/mypro/carnow/lib/examples/settings_test_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.dart", "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/account/models/user_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/account/providers/account_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/account/providers/account_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/account/providers/auth_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/account/providers/auth_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/complete_profile_required_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/edit_profile_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/login_required_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/profile_completion_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/unified_profile_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/screens/unified_subscription_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/account/widgets/account_list_tile.dart", "/Users/<USER>/mypro/carnow/lib/features/account/widgets/section_header.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/models/admin_financial_models.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_financial_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_financial_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_wallet_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/providers/admin_wallet_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_financial_dashboard_screen.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_subscription_requests_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/screens/admin_wallet_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/admin_user_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/models.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/models/user_status.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_management_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_management_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_user_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/admin_user_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/providers/providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/repositories/admin_user_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/admin_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/admin_users_list_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/screens.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/screens/user_actions_dialog.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/users/widgets/admin_settings_widget.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/model_year.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/models.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_generation.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_make.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_makes_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_specification.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/models/vehicle_trim.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/add_car_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/add_car_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/vehicle_list_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/providers/vehicle_list_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/repositories.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_generation_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_make_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_model_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/repositories/vehicle_trim_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/screens/add_car_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/vehicles/screens/screens.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_alerts_summary.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_dashboard_card.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_financial_chart.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_recent_actions.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/admin_system_health.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_details_card.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_filters_dialog.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_operations_dialog.dart", "/Users/<USER>/mypro/carnow/lib/features/admin_tools/widgets/wallet_search_bar.dart", "/Users/<USER>/mypro/carnow/lib/features/analytics/screens/analytics_dashboard_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/auction_localizations_extensions.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/models/bid_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/providers/auction_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/providers/auction_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/screens/auction_detail_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/screens/auction_list_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/auction_timer_widget.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/bid_history_widget.dart", "/Users/<USER>/mypro/carnow/lib/features/auction/widgets/bid_stats_widget.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/change_password_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/email_verification_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/forgot_password_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/new_login_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/reset_password_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/auth/screens/unified_auth_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/browse/routes/browse_routes.dart", "/Users/<USER>/mypro/carnow/lib/features/browse/screens/browse_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_item_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/models/cart_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/providers/cart_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/providers/cart_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/screens/cart_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/cart/widgets/cart_item_card.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/models/category_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/providers/category_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/providers/category_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/providers/simple_category_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/providers/simple_category_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/repositories/category_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/screens/category_browser_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/screens/category_products_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/widgets/category_card.dart", "/Users/<USER>/mypro/carnow/lib/features/categories/widgets/category_tile.dart", "/Users/<USER>/mypro/carnow/lib/features/favorites/providers/favorites_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/favorites/providers/favorites_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/favorites/routes/favorites_routes.dart", "/Users/<USER>/mypro/carnow/lib/features/favorites/screens/favorites_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/favorites/widgets/favorite_button.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/simple_vehicle.g.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/user_vehicle.g.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/models/vehicle_data_models.g.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/repositories/garage_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/routes/garage_routes.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/screens/simplified_add_vehicle_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/screens/smart_garage_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.dart", "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/history/models/history_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/history/providers/history_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/history/providers/history_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/history/screens/history_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/history/widgets/history_item.dart", "/Users/<USER>/mypro/carnow/lib/features/inventory/screens/inventory_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/legal/screens/privacy_policy_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/legal/screens/terms_conditions_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/blocked_user_model.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_conversation.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_conversation.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_message.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_message.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_models.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/models/chat_models.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/archived_chats_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/archived_chats_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/blocked_users_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/blocked_users_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/chat_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/providers/chat_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/archived_chats_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/blocked_users_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_backup_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_list_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_privacy_settings_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_settings_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/chat_storage_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/screens/new_chat_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/services/chat_service.dart", "/Users/<USER>/mypro/carnow/lib/features/messaging/widgets/chat_list_item.dart", "/Users/<USER>/mypro/carnow/lib/features/notifications/models/notification_model.dart", "/Users/<USER>/mypro/carnow/lib/features/notifications/screens/notifications_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/notifications/services/notification_service.dart", "/Users/<USER>/mypro/carnow/lib/features/notifications/services/notification_service.g.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/models/models.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/models/unified_order_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/providers/unified_orders_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/providers/unified_orders_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/repositories/unified_orders_repository.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/repositories/unified_orders_repository.g.dart", "/Users/<USER>/mypro/carnow/lib/features/orders/screens/my_orders_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/providers/spec_filter_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/providers/spec_filter_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/routes/subcategory_routes.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/home_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/product_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/accessories_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/auto_parts_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/electronics_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/maintenance_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/specialized_product_details_factory.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/tools_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/specialized/vehicles_details_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/screens/subcategory_products_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/widgets/dynamic_filter_sidebar.dart", "/Users/<USER>/mypro/carnow/lib/features/products/listings/widgets/product_card.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_filter_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/products/models/product_sort_by.dart", "/Users/<USER>/mypro/carnow/lib/features/products/providers/products_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/products/providers/products_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/products/services/product_api_service.dart", "/Users/<USER>/mypro/carnow/lib/features/products/services/product_api_service.g.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/models/recommendation_model.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/providers/recommendations_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/providers/recommendations_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/routes/recommendation_routes.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/screens/recommendations_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/services/recommendation_service.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/widgets/recommendation_card.dart", "/Users/<USER>/mypro/carnow/lib/features/recommendations/widgets/recommendation_group_widget.dart", "/Users/<USER>/mypro/carnow/lib/features/reports/screens/sales_reports_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/search/providers/search_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/search/providers/search_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/search/screens/search_results_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/search/screens/search_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/activity_item_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_dashboard_stats_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_enums.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_notification_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/seller_profile_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/models/subscription_request_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/admin_subscription_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/admin_subscription_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_dashboard_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_dashboard_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_notifications_async_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_notifications_async_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_profile_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_profile_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_stats_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/seller_stats_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_request_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/providers/subscription_request_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/add_product_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/order_management_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/products/product_edit_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/sales_analytics_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/seller_dashboard_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/seller_terms_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/subscription_request_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/screens/subscription_request_status_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/services/notifications_api_service.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/services/notifications_api_service.g.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/widgets/notification_badge.dart", "/Users/<USER>/mypro/carnow/lib/features/seller/widgets/product_list_item.dart", "/Users/<USER>/mypro/carnow/lib/features/settings/screens/developer_tools_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/settings/screens/settings_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/store/screens/store_screen.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/category_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/section_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.freezed.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/models/subcategory_model.g.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/providers/taxonomy_providers.dart", "/Users/<USER>/mypro/carnow/lib/features/taxonomy/providers/taxonomy_providers.g.dart", "/Users/<USER>/mypro/carnow/lib/features/wallet/screens/modern_wallet_screen.dart", "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations.dart", "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations_ar.dart", "/Users/<USER>/mypro/carnow/lib/l10n/app_localizations_en.dart", "/Users/<USER>/mypro/carnow/lib/main.dart", "/Users/<USER>/mypro/carnow/lib/navigation/models/navigation_item.dart", "/Users/<USER>/mypro/carnow/lib/navigation/models/navigation_route.dart", "/Users/<USER>/mypro/carnow/lib/navigation/providers/navigation_provider.dart", "/Users/<USER>/mypro/carnow/lib/navigation/providers/navigation_route_provider.dart", "/Users/<USER>/mypro/carnow/lib/navigation/widgets/main_layout.dart", "/Users/<USER>/mypro/carnow/lib/shared/providers/location_provider.dart", "/Users/<USER>/mypro/carnow/lib/shared/providers/location_provider.g.dart", "/Users/<USER>/mypro/carnow/lib/shared/screens/support_screen.dart", "/Users/<USER>/mypro/carnow/lib/shared/utils/app_styles.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/app_loading_indicator.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/empty_state_widget.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/error_message.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/error_view.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_button.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_indicator.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/loading_widget.dart", "/Users/<USER>/mypro/carnow/lib/shared/widgets/primary_button.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/connectivity_plus.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/connectivity_plus_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/dbus.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_address.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_server.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_bus_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_error_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_interface_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspectable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_match_rule.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_member_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_message.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_call.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_tree.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_peer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_properties.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_read_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_server.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_signal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_uuid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_value.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_write_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/dio.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapters/io_adapter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/cancel_token.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio/dio_for_native.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/form_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/headers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/imply_content_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/log.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file/io_multipart_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/parameter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/progress_stream/io_progress_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/redirect_record.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response/response_stream_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/background_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/fused_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/sync_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/consolidate_bytes.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/transform_empty_to_null.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/dynamic_color.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/corepalette_to_colorscheme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/dynamic_color_builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/dynamic_color_plugin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/src/harmonization.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/file_picker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/exceptions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_macos.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/file_picker_result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/dialog_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/file_picker_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/kdialog_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/linux/qarma_and_zenity_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/platform_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/windows/file_picker_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/src/windows/file_picker_windows_ffi_types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/file_selector_macos.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flutter_version.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/scribe.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/expansible.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/flutter_dotenv.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/dotenv.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/flutter_hooks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/framework.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/hooks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/misc.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/platform_brightness.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/primitives.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/widgets_binding_observer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/animation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/async.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/carousel_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/debounced.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/draggable_scrollable_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/expansion_tile_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/fixed_extent_scroll_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/focus_node.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/focus_scope_node.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/keep_alive.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/listenable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/listenable_selector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/page_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/scroll_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/search_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/tab_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/text_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/transformation_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/tree_sliver_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/src/widget_states_controller.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/flutter_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/cupertino_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_cupertino_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_date_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_material_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/l10n/generated_widgets_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/material_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/utils/date_localizations.dart", "/Users/<USER>/development/flutter/packages/flutter_localizations/lib/src/widgets_localizations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/flutter_vector_icons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/ant_design.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/entypo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/evil_icons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/feather.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_brands.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_regular.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/font_awesome_5_solid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/fontisto.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/ionicons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/material_community_icons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/material_icons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/octicons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/simple_line_icons.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/src/zocial.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/lib/freezed_annotation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/lib/freezed_annotation.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/geoclue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/accuracy_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/geoclue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/manager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/simple.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/location.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/src/util.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/geolocator_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/geolocator_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/android_position.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/android_settings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/src/types/foreground_settings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/geolocator_apple.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/geolocator_apple.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/activity_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/apple_settings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/geolocator_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geoclue_x.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geolocator_gnome.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/src/geolocator_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/geolocator_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/enums.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy_status.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_permission.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_service.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/activity_missing_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/already_subscribed_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/invalid_permission_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/location_service_disabled_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_definitions_not_found_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_denied_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_request_in_progress_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/position_update_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/integer_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/geolocator_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/implementations/method_channel_geolocator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/location_settings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/models.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/position.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/go_router.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/configuration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/delegate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/information_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/logging.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/match.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/error_screen.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/inherited_router.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/cupertino.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/custom_transition_page.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/material.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/path_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/router.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/state.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/google_sign_in.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/event_types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/fife.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/identity_types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/src/token_types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/widgets.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/lib/google_sign_in_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/lib/google_sign_in_ios.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/google_sign_in_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/src/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/gsettings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/dconf_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/getuid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/getuid_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_backend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_dconf_backend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_keyfile_backend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gsettings_memory_backend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_binary_codec.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_database.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/src/gvariant_text_codec.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/gtk.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application_notifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings_real.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/hooks_riverpod.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/src/consumer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/src/internals.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/io_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/image_picker_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/date_symbol_data_custom.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/date_symbols.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/intl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/date_format_internal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/global_state.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi_formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_computation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format_field.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/micro_money.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/compact_number_format.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format_parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser_base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/regexp.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/string_stack.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/text_direction.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/plural_rules.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/_internal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/jni.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/accessors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jarray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jimplementer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jni.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jobject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jreference.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jvalues.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/kotlin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jboolean.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jbyte.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jcharacter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jdouble.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jfloat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jinteger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jlong.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jnumber.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jshort.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/jstring.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/lang/lang.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/method_invocation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/jbuffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/jbyte_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/nio/nio.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/generated_bindings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/global_env_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/third_party/jni_bindings_generated.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jclass.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/jprimitives.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jiterator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jlist.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jmap.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/jset.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/src/util/util.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/local_auth_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/src/auth_messages_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/local_auth_darwin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_ios.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_macos.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/default_method_channel_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/local_auth_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/biometric_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/local_auth_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/types/auth_messages_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/ansi_color.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/date_time_format.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/filters/development_filter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/filters/production_filter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_filter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/log_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/output_event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/advanced_file_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/console_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/file_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/memory_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/multi_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/outputs/stream_output.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/hybrid_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/logfmt_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/prefix_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/pretty_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/src/printers/simple_printer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/web.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/blend/blend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/contrast/contrast.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dislike/dislike_analyzer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/dynamic_color.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/dynamic_scheme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/material_dynamic_colors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/src/contrast_curve.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/src/tone_delta_pair.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/dynamiccolor/variant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/cam16.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/hct.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/src/hct_solver.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/hct/viewing_conditions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/material_color_utilities.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/palettes/core_palette.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/palettes/tonal_palette.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_celebi.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_wsmeans.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/quantizer_wu.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/src/point_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/quantize/src/point_provider_lab.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_content.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_expressive.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_fidelity.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_fruit_salad.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_monochrome.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_neutral.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_rainbow.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_tonal_spot.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/scheme/scheme_vibrant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/score/score.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/temperature/temperature_cache.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/color_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/math_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/utils/string_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/mime.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/bound_multipart_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/char_code.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/default_extension_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/extension.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/magic_number.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_multipart_transformer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_shared.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/nm.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/src/network_manager_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/package_info_plus.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_attribute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_version_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/method_channel_package_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/core.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/definition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/expression.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/matcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/petitparser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/token.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/grammar.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/reference.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/undefined.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/reference.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/resolve.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/group.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/accept.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_match.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_pattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast_list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/continuation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/flatten.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/permute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/pick.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/token.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/trimming.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/where.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/any_of.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/char.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/code.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/constant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/digit.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/letter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lookup.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lowercase.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/none_of.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/not.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/optimize.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/pattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/predicate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/range.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/uppercase.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/whitespace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/word.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/and.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/choice.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/delegate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_6.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_7.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_8.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_9.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/not.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/optional.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/sequence.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/settable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/skip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/eof.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/epsilon.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/failure.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/label.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/newline.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/position.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/any.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/character.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/pattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/predicate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/string.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/character.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/greedy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/lazy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/limited.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/possessive.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/repeating.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated_by.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/unbounded.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/failure_joiner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/labeled.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/resolvable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/separated_list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/sequential.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/reflection/iterable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/annotations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/lib/riverpod_annotation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/lib/src/riverpod_annotation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/sentry.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/client_report.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/client_report_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/discard_reason.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/discarded_event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/client_reports/noop_client_report_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/dart_exception_type_identifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/dart_exception_type_identifier_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/diagnostic_log.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/_io_environment_variables.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/_web_environment_variables.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/environment_variables.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/environment/keys.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/deduplication_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/enricher_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/flutter_runtime.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/io_enricher_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/enricher/io_platform_memory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/exception_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/exception_group_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/exception/io_exception_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/event_processor/run_event_processors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_cause.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_cause_extractor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_stacktrace_extractor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/exception_type_identifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/feature_flags_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hint.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/breadcrumb_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/client_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/failed_request_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/io_client_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/sentry_http_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/sentry_http_client_error.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/http_client/tracing_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hub.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/hub_adapter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/invalid_sentry_trace_header_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/isolate_error_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/lifecycle/on_before_capture_log.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/load_dart_debug_images_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/logs_enricher_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_hub.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_log_batcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/noop_sentry_span.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/observers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/origin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/origin_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/performance_collector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/platform/_io_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/platform/platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/profiling.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/propagation_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/access_aware_map.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/breadcrumb.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/contexts.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/debug_image.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/debug_meta.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/dsn.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/max_body_size.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/mechanism.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sdk_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sdk_version.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_app.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_baggage_header.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_browser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_culture.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_device.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_event_like.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feature_flag.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feature_flags.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_feedback.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_geo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_gpu.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_id.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log_attribute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_log_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_message.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_operating_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_package.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_proxy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_request.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_response.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_runtime.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_span.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_stack_frame.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_stack_trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_thread.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_trace_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_trace_header.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_transaction_name_source.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_user.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_view_hierarchy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/sentry_view_hierarchy_element.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/span_id.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/protocol/span_status.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/recursive_exception_cause_extractor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/run_zoned_guarded_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/runtime_checker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/scope.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/scope_observer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_attachment/sentry_attachment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_baggage.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_client.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_header.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_item.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_envelope_item_header.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_exception_factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_isolate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_isolate_extension.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_item_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_log_batcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_logger_formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_measurement.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_measurement_unit.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_run_zoned_guarded.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_sampling_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_span_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_span_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_stack_trace_factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_template_string.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_trace_context_header.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_trace_origins.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_tracer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_tracer_finish_status.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_traces_sampler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_traces_sampling_decision.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/sentry_transaction_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/span_data_convention.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/spotlight.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/throwable_mechanism.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/tracing.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/client_report_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/data_category.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/encode.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/http_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/http_transport_request_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/noop_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limit.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limit_parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/rate_limiter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/spotlight_http_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/task_queue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/transport/transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/type_check_hint.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/_io_get_isolate_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/_io_get_sentry_operating_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/breadcrumb_log_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/http_header_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/http_sanitizer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/isolate_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/iterable_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/os_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/regex_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/sample_rate_format.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/stacktrace_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/tracing_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/transport_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/utils/url_details.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/src/version.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/sentry_flutter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/binding_wrapper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/android_platform_exception_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/flutter_enricher_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/flutter_exception_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/platform_exception_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/replay_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/screenshot_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/url_filter/io_url_filter_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/url_filter/url_filter_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/event_processor/widget_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_feedback_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_feedback_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/feedback/sentry_logo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/file_system_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/flutter_exception_type_identifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/flutter_sentry_attachment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frame_callback_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frames_tracking/sentry_delayed_frames_tracker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/frames_tracking/span_frame_metrics_collector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/connectivity_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/connectivity_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/connectivity/noop_connectivity_provider.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/debug_print_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/flutter_error_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/flutter_framework_feature_flag_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/frames_tracking_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/integrations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_contexts_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_debug_images_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/load_release_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_app_start_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_app_start_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_load_debug_images_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/native_sdk_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/on_error_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/screenshot_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/sdk_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/web_session_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/widgets_binding_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/integrations/widgets_flutter_binding_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/jvm/jvm_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/jvm/jvm_frame.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/binding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/sentry_native.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_invoker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/c/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/binding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/cocoa_replay_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/cocoa/sentry_native_cocoa.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/factory_real.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/android_replay_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/binding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/java/sentry_native_java.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/method_channel_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_app_start.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_memory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/native_scope_observer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_binding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_native_channel.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/native/sentry_safe_method_channel.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_display.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_display_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/sentry_navigator_observer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_display_tracker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_full_display_tracker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/navigation/time_to_initial_display_tracker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/profiling.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/renderer/io_renderer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/renderer/renderer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_config.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_quality.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/replay_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduled_recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduled_recorder_config.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/replay/scheduler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/masking_config.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/recorder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/recorder_config.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/screenshot.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/screenshot_support.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_mask_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_screenshot_quality.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_screenshot_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/sentry_unmask_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/screenshot/widget_filter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_asset_bundle.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_flutter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_flutter_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_privacy_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_replay_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/sentry_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/user_interaction/sentry_user_interaction_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/user_interaction/user_interaction_info.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/debouncer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/platform_dispatcher_wrapper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/utils/timer_debouncer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/version.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/sentry_tree_walker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/view_hierarchy_event_processor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/view_hierarchy/view_hierarchy_integration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/web/javascript_transport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/web/web_session_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/widget_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/src/widgets_binding_observer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/share_plus.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/share_plus_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/share_plus_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/src/windows_version_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/method_channel/method_channel_share.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/platform_interface/share_plus_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/share_plus_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/smooth_page_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/color_transition_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/customizable_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/expanding_dots_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/indicator_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/jumping_dot_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/scale_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/scrolling_dots_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/slide_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/swap_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/effects/worm_effect.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/color_transition_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/customizable_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/expanding_dots_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/indicator_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/jumping_dot_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scale_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scrolling_dots_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/scrolling_dots_painter_with_fixed_center.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/slide_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/swap_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/painters/worm_painter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/src/smooth_page_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite_logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sql.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqlite_api.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/arg_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/batch.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/collection_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/compat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/constant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/cursor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_ext.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/dev_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/env_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/logger/sqflite_logger.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/constant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/import_mixin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/open_options.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/path_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform_io.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_database_factory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_debug.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_command.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/transaction.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/value_utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/utils/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/charts.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/base.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/category_axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/datetime_axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/datetime_category_axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/logarithmic_axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/multi_level_labels.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/numeric_axis.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/axis/plot_band.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/element_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/crosshair.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/trackball.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/behaviors/zooming.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/cartesian_chart.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/circular_chart.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/annotation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/callbacks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/chart_point.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/circular_data_label.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/circular_data_label_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/connector_line.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/core_legend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/core_tooltip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/data_label.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/empty_points.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/funnel_data_label.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/interactive_tooltip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/layout_handler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/legend.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/marker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/pyramid_data_label.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/common/title.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/funnel_chart.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/accumulation_distribution_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/atr_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/bollinger_bands_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/ema_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/macd_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/momentum_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/roc_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/rsi_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/sma_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/stochastic_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/technical_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/tma_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/indicators/wma_indicator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/behavior.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/selection.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/interactions/tooltip.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/pyramid_chart.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/area_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/chart_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/bar_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/box_and_whisker_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/bubble_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/candle_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/column_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/doughnut_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/error_bar_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/fast_line_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/funnel_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/hilo_open_close_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/hilo_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/histogram_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/line_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/pie_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/pyramid_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/radial_bar_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/range_area_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/range_column_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/scatter_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/spline_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_area100_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_area_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_bar100_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_bar_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_column100_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_column_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_line100_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stacked_line_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/step_area_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/stepline_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/series/waterfall_series.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/trendline/trendline.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/enum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/renderer_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/typedef.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/charts/utils/zooming_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/marker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/utils/enum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/src/sparkline/utils/helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/core.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/calendar/hijri_date_time.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/license.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/utils/helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/calendar/calendar_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/localizations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/localizations/global_localizations.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/slider_controller.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/assistview_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/barcodes_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/calendar_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/charts_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/chat_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/color_scheme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/datagrid_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/datapager_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/daterangepicker_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/gauges_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/maps_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/pdfviewer_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/range_selector_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/range_slider_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/slider_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/spark_charts_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/theme_widget.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/theme/treemap_theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/src/utils/shape_helper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/theme.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/am_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ar_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/az_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/be_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/bn_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/bs_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ca_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/cs_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/da_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/de_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/dv_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/en_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/es_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/et_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fa_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fi_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/fr_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/gr_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/he_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hi_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hr_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/hu_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/id_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/it_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ja_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/km_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ko_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ku_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/lookupmessages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/lv_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/mn_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ms_my_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/my_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nb_no_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nl_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/nn_no_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/pl_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/pt_br_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ro_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ru_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/rw_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/sr_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/sv_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ta_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/th_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/tk_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/tr_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/uk_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/ur_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/vi_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/zh_cn_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/messages/zh_messages.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/src/timeago.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/timeago.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/colors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/frustum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/intersection_result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/noise.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/obb3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/plane.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quad.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quaternion.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/ray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/sphere.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/triangle.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/error_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/opengl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/utilities.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/bstr.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/callbacks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iagileobject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iapplicationactivationmanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfactory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfile.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfilesenumerator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplication.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestospackagedependency.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependency.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackageid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestproperties.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader6.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader7.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxpackagereader.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiocaptureclient.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclientduckingcontrol.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclockadjustment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiorenderclient.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionenumerator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiostreamvolume.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ibindctx.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ichannelaudiovolume.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iclassfactory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpoint.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpointcontainer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idesktopwallpaper.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idispatch.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumidlist.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienummoniker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworkconnections.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworks.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumresources.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumspellingerror.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumstring.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumvariant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumwbemclassobject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ierrorinfo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialogcustomize.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileisinuse.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileopendialog.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifilesavedialog.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinitializewithwindow.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinspectable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfolder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfoldermanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataassemblyimport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenserex.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevice.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevicecollection.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdeviceenumerator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immendpoint.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immnotificationclient.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imodalwindow.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imoniker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetwork.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworkconnection.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanagerevents.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersist.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistfile.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistmemory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersiststream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipropertystore.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iprovideclassinfo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irestrictederrorinfo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irunningobjecttable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensorcollection.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensordatareport.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensormanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isequentialstream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellfolder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemarray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemfilter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemimagefactory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemresources.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdatalist.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdual.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellservice.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isimpleaudiovolume.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechaudioformat.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechbasestream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttoken.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttokens.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoice.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoicestatus.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechwaveformatex.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerfactory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellingerror.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeventsource.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispnotifysource.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispvoice.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/istream.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isupporterrorinfo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/itypeinfo.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation6.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationandcondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationannotationpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationboolcondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcacherequest.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdockpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdragpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdroptargetpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement4.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement5.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement6.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement7.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement8.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement9.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelementarray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgriditempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgridpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationinvokepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationnotcondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationorcondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationpropertycondition.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactory.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationrangevaluepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollitempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionitempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationstylespattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtableitempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtablepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextchildpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtexteditpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange3.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrangearray.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtogglepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern2.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtreewalker.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvaluepattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationwindowpattern.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iunknown.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuri.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ivirtualdesktopmanager.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemclassobject.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemconfigurerefresher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemcontext.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemhiperfenum.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemlocator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemobjectaccess.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemrefresher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemservices.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwinhttprequest.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/combase.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_metadata.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_nodoc.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/dispatcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/exceptions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/_internal.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/dialogs.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/filetime.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/int_to_hexstring.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/list_to_blob.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_ansi.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string_array.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/unpack_utf16.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/functions.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/guid.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/inline.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/macros.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/propertykey.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/types.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/utils.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/variant.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/advapi32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_path_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bluetoothapis.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bthprops.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comctl32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comdlg32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/crypt32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dbghelp.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dwmapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dxva2.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/gdi32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/iphlpapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/kernel32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/magnification.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/netapi32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ntdll.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ole32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/oleaut32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/powrprof.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/propsys.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/rometadata.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/scarddlg.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/setupapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shell32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shlwapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/user32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/uxtheme.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/version.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wevtapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winmm.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winscard.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winspool.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wlanapi.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wtsapi32.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/xinput1_4.g.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winmd_constants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winrt_helpers.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/win32.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/dart_plugin_registrant.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/providers/garage_provider.dart", "/Users/<USER>/mypro/carnow/lib/features/garage/providers/garage_provider.g.dart"], "outputs": ["/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/93d1567421891a764f29836c87e2480e/app.dill", "/Users/<USER>/mypro/carnow/.dart_tool/flutter_build/93d1567421891a764f29836c87e2480e/app.dill"]}