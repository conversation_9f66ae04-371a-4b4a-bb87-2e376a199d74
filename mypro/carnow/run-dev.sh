#!/bin/bash

# =============================================================================
# CarNow Flutter App - Development Mode (Local Backend Only)
# =============================================================================
#
# This script starts Flutter in DEVELOPMENT mode
# - Connects to LOCAL backend only (http://********:8080)
# - For production, use normal: flutter run --release
#
# Usage: ./run-dev.sh
#

set -e  # Exit on any error

echo "📱 Starting CarNow Flutter App - DEVELOPMENT MODE"
echo "=================================================="

# Check if backend is running
echo "🔍 Checking if local backend is running..."
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Local backend is running on port 8080"
    echo "📱 Flutter will connect to: http://********:8080"
else
    echo "⚠️  Local backend is not running!"
    echo ""
    echo "🚀 To start the backend, run:"
    echo "   cd backend-go && ./run-dev.sh"
    echo ""
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cancelled. Please start the backend first."
        exit 1
    fi
fi

echo ""
echo "🔄 Starting Flutter in development mode..."
echo "🔥 Hot reload enabled - connects to LOCAL backend"
echo "🛑 Press 'r' to hot reload, 'R' to hot restart, 'q' to quit"
echo ""

# Start Flutter in development mode with local backend
flutter run \
    --debug \
    --hot \
    --dart-define=API_BASE_URL=http://********:8080
