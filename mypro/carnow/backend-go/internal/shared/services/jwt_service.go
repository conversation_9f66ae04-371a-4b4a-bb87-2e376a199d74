package services

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"log"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService handles secure JWT operations with production-ready features
// Task 1.2 Requirement: Implement secure key generation and storage
type JWTService struct {
	config          *config.Config
	privateKey      *rsa.PrivateKey
	publicKey       *rsa.PublicKey
	keyID           string
	revokedTokens   map[string]time.Time // In-memory token blacklist (use Redis in production)
	keyRotationTime time.Time
}

// EnhancedJWTClaims represents the enhanced claims structure for JWT tokens
// Task 1: Update JWT claims structure to include all required fields
type EnhancedJWTClaims struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	Name      string `json:"name"`
	Provider  string `json:"provider"`
	IsAd<PERSON>   bool   `json:"is_admin"`
	TokenType string `json:"token_type"` // "access" or "refresh"
	jwt.RegisteredClaims
}

// JWTClaims represents the legacy claims structure (kept for backward compatibility)
type JWTClaims struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	TokenType string `json:"token_type"` // "access" or "refresh"
	jwt.RegisteredClaims
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// NewJWTService creates a new JWT service with secure configuration
func NewJWTService(cfg *config.Config) (*JWTService, error) {
	service := &JWTService{
		config: cfg,
	}

	// Initialize RSA keys for secure JWT signing
	if err := service.initializeKeys(); err != nil {
		return nil, fmt.Errorf("failed to initialize JWT keys: %w", err)
	}

	return service, nil
}

// initializeKeys sets up RSA keys for JWT signing with enhanced security
// Task 1.2 Requirement: Implement secure key generation and storage
func (j *JWTService) initializeKeys() error {
	// Check if we have a JWT secret configured
	if j.config.JWT.Secret == "" {
		return fmt.Errorf("JWT secret not configured")
	}

	// Initialize revoked tokens map
	j.revokedTokens = make(map[string]time.Time)

	// Generate unique key ID for this instance
	j.keyID = fmt.Sprintf("carnow-jwt-key-%d", time.Now().Unix())

	// Try to load existing keys from environment or generate new ones
	if err := j.loadOrGenerateKeys(); err != nil {
		return fmt.Errorf("failed to initialize JWT keys: %w", err)
	}

	// Set key rotation time (rotate keys every 30 days in production)
	j.keyRotationTime = time.Now().Add(30 * 24 * time.Hour)

	return nil
}

// GenerateEnhancedJWT creates a new JWT token with enhanced claims structure
// Task 1: Update JWT claims structure to include all required fields
func (j *JWTService) GenerateEnhancedJWT(userID, email, name, provider string, isAdmin bool) (string, error) {
	now := time.Now()

	// Create enhanced claims with all required fields
	claims := EnhancedJWTClaims{
		UserID:    userID,
		Email:     email,
		Name:      name,
		Provider:  provider,
		IsAdmin:   isAdmin,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			Audience:  []string{j.config.JWT.Audience},
			Issuer:    j.config.JWT.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.config.JWT.ExpiresIn)), // Use config duration
		},
	}

	// Sign token with consistent algorithm and secret
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token.Header["kid"] = j.keyID

	tokenString, err := token.SignedString([]byte(j.config.JWT.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to sign enhanced JWT token: %w", err)
	}

	log.Printf("✅ JWT: Generated enhanced token for user %s (expires in %v)", userID, j.config.JWT.ExpiresIn)
	return tokenString, nil
}

// GenerateTokenPair creates a new access and refresh token pair (legacy method)
func (j *JWTService) GenerateTokenPair(userID, email, role string) (*TokenPair, error) {
	now := time.Now()

	// Generate access token with proper expiration from config
	accessClaims := JWTClaims{
		UserID:    userID,
		Email:     email,
		Role:      role,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			Audience:  []string{j.config.JWT.Audience},
			Issuer:    j.config.JWT.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.config.JWT.ExpiresIn)), // Use config duration
		},
	}

	accessToken, err := j.signLegacyToken(accessClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// Generate refresh token with proper expiration from config
	refreshClaims := JWTClaims{
		UserID:    userID,
		Email:     email,
		Role:      role,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			Audience:  []string{j.config.JWT.Audience},
			Issuer:    j.config.JWT.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.config.JWT.RefreshExpiresIn)), // Use config duration
		},
	}

	refreshToken, err := j.signLegacyToken(refreshClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessClaims.ExpiresAt.Time,
		TokenType:    "Bearer",
	}, nil
}

// ValidateEnhancedToken validates and parses an enhanced JWT token
// Task 1: Ensure consistent signing algorithm and secret usage
func (j *JWTService) ValidateEnhancedToken(tokenString string) (*EnhancedJWTClaims, error) {
	// Validate token format first
	if err := j.validateTokenFormat(tokenString); err != nil {
		return nil, fmt.Errorf("invalid token format: %w", err)
	}

	// Parse token with enhanced claims using HMAC (consistent with generation)
	token, err := jwt.ParseWithClaims(tokenString, &EnhancedJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Ensure consistent signing method (HMAC-SHA256)
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		// Use JWT secret from config for validation
		return []byte(j.config.JWT.Secret), nil
	})

	if err != nil {
		log.Printf("❌ JWT: Token parsing failed: %v", err)
		return nil, fmt.Errorf("failed to parse enhanced token: %w", err)
	}

	claims, ok := token.Claims.(*EnhancedJWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid enhanced token claims")
	}

	// Check if token is revoked
	if j.IsTokenRevoked(claims.ID) {
		return nil, fmt.Errorf("token has been revoked")
	}

	// Enhanced validation with detailed error messages
	if err := j.validateEnhancedClaims(claims); err != nil {
		return nil, fmt.Errorf("enhanced claims validation failed: %w", err)
	}

	log.Printf("✅ JWT: Enhanced token validated successfully for user %s", claims.UserID)
	return claims, nil
}

// ValidateToken validates and parses a JWT token (legacy method)
func (j *JWTService) ValidateToken(tokenString string) (*JWTClaims, error) {
	// Validate token format first
	if err := j.validateTokenFormat(tokenString); err != nil {
		return nil, fmt.Errorf("invalid token format: %w", err)
	}

	// Parse token using HMAC (consistent with generation)
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Ensure consistent signing method (HMAC-SHA256)
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		// Use JWT secret from config for validation
		return []byte(j.config.JWT.Secret), nil
	})

	if err != nil {
		log.Printf("❌ JWT: Token parsing failed: %v", err)
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Check if token is revoked
	if j.IsTokenRevoked(claims.ID) {
		return nil, fmt.Errorf("token has been revoked")
	}

	// Additional validation
	if err := j.validateLegacyClaims(claims); err != nil {
		return nil, fmt.Errorf("invalid claims: %w", err)
	}

	return claims, nil
}

// validateTokenFormat validates the basic format of a JWT token
func (j *JWTService) validateTokenFormat(token string) error {
	if token == "" {
		return fmt.Errorf("token is empty")
	}

	// Basic JWT format validation (3 parts separated by dots)
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return fmt.Errorf("invalid JWT format: expected 3 parts, got %d", len(parts))
	}

	// Validate each part is base64 encoded
	for i, part := range parts {
		if len(part) == 0 {
			return fmt.Errorf("JWT part %d is empty", i+1)
		}
		// Try to decode to validate base64 format
		if _, err := base64.RawURLEncoding.DecodeString(part); err != nil {
			return fmt.Errorf("JWT part %d is not valid base64: %w", i+1, err)
		}
	}

	return nil
}

// RefreshToken refreshes an access token using a valid refresh token
// Task 1: Add proper token expiration and validation logic
func (j *JWTService) RefreshToken(refreshToken string) (*TokenPair, error) {
	// Validate refresh token format and structure
	if err := j.validateTokenFormat(refreshToken); err != nil {
		return nil, fmt.Errorf("invalid refresh token format: %w", err)
	}

	// Parse and validate refresh token using HMAC (consistent with generation)
	token, err := jwt.ParseWithClaims(refreshToken, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Ensure consistent signing method (HMAC-SHA256)
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		// Use JWT secret from config for validation
		return []byte(j.config.JWT.Secret), nil
	})

	if err != nil {
		log.Printf("❌ JWT: Refresh token parsing failed: %v", err)
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Extract and validate claims
	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid refresh token claims")
	}

	// Check if refresh token is revoked
	if j.IsTokenRevoked(claims.ID) {
		return nil, fmt.Errorf("refresh token has been revoked")
	}

	// Validate token type
	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Enhanced token timing validation
	now := time.Now()
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(now) {
		return nil, fmt.Errorf("refresh token has expired at %v", claims.ExpiresAt.Time)
	}

	if claims.IssuedAt != nil && claims.IssuedAt.After(now.Add(5*time.Minute)) {
		return nil, fmt.Errorf("refresh token issued too far in the future")
	}

	// Validate issuer and audience with detailed error messages
	if claims.Issuer != j.config.JWT.Issuer {
		return nil, fmt.Errorf("invalid token issuer: expected %s, got %s", j.config.JWT.Issuer, claims.Issuer)
	}

	if len(claims.Audience) == 0 || claims.Audience[0] != j.config.JWT.Audience {
		expectedAud := j.config.JWT.Audience
		actualAud := ""
		if len(claims.Audience) > 0 {
			actualAud = claims.Audience[0]
		}
		return nil, fmt.Errorf("invalid token audience: expected %s, got %s", expectedAud, actualAud)
	}

	// Check if user ID is valid
	if claims.UserID == "" {
		return nil, fmt.Errorf("invalid user ID in refresh token")
	}

	// Revoke the old refresh token to prevent reuse
	if err := j.RevokeToken(claims.ID); err != nil {
		log.Printf("⚠️ JWT: Failed to revoke old refresh token: %v", err)
	}

	// Generate new token pair
	newTokenPair, err := j.GenerateTokenPair(claims.UserID, claims.Email, claims.Role)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new token pair: %w", err)
	}

	log.Printf("✅ JWT: Token refreshed successfully for user: %s", claims.UserID)
	return newTokenPair, nil
}

// RevokeToken adds a token to the revocation list
// Task 1.2 Requirement: Add token blacklisting capability for logout functionality
func (j *JWTService) RevokeToken(tokenID string) error {
	if tokenID == "" {
		return fmt.Errorf("token ID cannot be empty")
	}

	// Add to in-memory blacklist with expiration time
	// In production, this should use Redis for distributed systems
	j.revokedTokens[tokenID] = time.Now().Add(7 * 24 * time.Hour) // Keep for 7 days

	// Clean up expired tokens to prevent memory leaks
	go j.cleanupExpiredTokens()

	log.Printf("🚫 JWT: Token revoked: %s", tokenID[:8]+"...")
	return nil
}

// IsTokenRevoked checks if a token has been revoked
// Task 1.2 Requirement: Add token blacklisting capability for logout functionality
func (j *JWTService) IsTokenRevoked(tokenID string) bool {
	if tokenID == "" {
		return false
	}

	expiration, exists := j.revokedTokens[tokenID]
	if !exists {
		return false
	}

	// Check if the revocation has expired
	if time.Now().After(expiration) {
		delete(j.revokedTokens, tokenID)
		return false
	}

	return true
}

// cleanupExpiredTokens removes expired tokens from the blacklist
func (j *JWTService) cleanupExpiredTokens() {
	now := time.Now()
	for tokenID, expiration := range j.revokedTokens {
		if now.After(expiration) {
			delete(j.revokedTokens, tokenID)
		}
	}
}

// loadOrGenerateKeys initializes JWT signing configuration
// Task 1: Ensure consistent signing algorithm and secret usage
func (j *JWTService) loadOrGenerateKeys() error {
	// Validate JWT secret is configured
	if j.config.JWT.Secret == "" {
		return fmt.Errorf("JWT secret is required but not configured")
	}

	// Validate secret strength
	if len(j.config.JWT.Secret) < 32 {
		log.Printf("⚠️ JWT: Secret is shorter than recommended (32+ characters)")
	}

	// For HMAC-SHA256, we don't need to generate RSA keys
	// The secret from config is used directly for signing and validation

	// Initialize revoked tokens map if not already done
	if j.revokedTokens == nil {
		j.revokedTokens = make(map[string]time.Time)
	}

	log.Printf("✅ JWT: HMAC-SHA256 signing initialized for %s environment", j.config.App.Environment)
	log.Printf("✅ JWT: Using JWT secret from configuration (length: %d characters)", len(j.config.JWT.Secret))

	return nil
}

// signLegacyToken signs a legacy JWT token using HMAC-SHA256
// Task 1: Ensure consistent signing algorithm and secret usage
func (j *JWTService) signLegacyToken(claims JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Add key ID to header for key rotation support
	token.Header["kid"] = j.keyID

	tokenString, err := token.SignedString([]byte(j.config.JWT.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to sign legacy token: %w", err)
	}

	return tokenString, nil
}

// signToken signs a JWT token using RSA private key (legacy RSA method)
// Task 1.2 Requirement: Enhanced token signing with key rotation support
func (j *JWTService) signToken(claims JWTClaims) (string, error) {
	// Use HMAC-SHA256 for consistency instead of RSA
	return j.signLegacyToken(claims)
}

// validateEnhancedClaims performs comprehensive validation on enhanced JWT claims
// Task 1: Add proper token expiration and validation logic
func (j *JWTService) validateEnhancedClaims(claims *EnhancedJWTClaims) error {
	now := time.Now()

	// Check expiration with detailed error message
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(now) {
		return fmt.Errorf("token has expired at %v (current time: %v)", claims.ExpiresAt.Time, now)
	}

	// Check not before with detailed error message
	if claims.NotBefore != nil && claims.NotBefore.After(now) {
		return fmt.Errorf("token not valid until %v (current time: %v)", claims.NotBefore.Time, now)
	}

	// Check issued at time for reasonable bounds
	if claims.IssuedAt != nil {
		// Token should not be issued more than 1 hour in the future (clock skew tolerance)
		if claims.IssuedAt.After(now.Add(time.Hour)) {
			return fmt.Errorf("token issued too far in the future: %v", claims.IssuedAt.Time)
		}
		// Token should not be older than configured expiration + refresh expiration
		maxAge := j.config.JWT.ExpiresIn + j.config.JWT.RefreshExpiresIn
		if claims.IssuedAt.Before(now.Add(-maxAge)) {
			return fmt.Errorf("token is too old: issued at %v", claims.IssuedAt.Time)
		}
	}

	// Check issuer with detailed error message
	if claims.Issuer != j.config.JWT.Issuer {
		return fmt.Errorf("invalid issuer: expected %s, got %s", j.config.JWT.Issuer, claims.Issuer)
	}

	// Check audience with detailed error message
	if len(claims.Audience) == 0 || claims.Audience[0] != j.config.JWT.Audience {
		expectedAud := j.config.JWT.Audience
		actualAud := ""
		if len(claims.Audience) > 0 {
			actualAud = claims.Audience[0]
		}
		return fmt.Errorf("invalid audience: expected %s, got %s", expectedAud, actualAud)
	}

	// Check required fields with specific error messages
	if claims.UserID == "" {
		return fmt.Errorf("user ID is required but missing")
	}

	if claims.Email == "" {
		return fmt.Errorf("email is required but missing")
	}

	// Validate email format (basic check)
	if !strings.Contains(claims.Email, "@") {
		return fmt.Errorf("invalid email format: %s", claims.Email)
	}

	// Validate provider field
	if claims.Provider == "" {
		return fmt.Errorf("provider is required but missing")
	}

	// Validate token type
	if claims.TokenType != "access" && claims.TokenType != "refresh" {
		return fmt.Errorf("invalid token type: %s", claims.TokenType)
	}

	// Note: We don't validate UUID format for UserID as Google IDs are strings, not UUIDs

	log.Printf("✅ JWT: Enhanced claims validation passed for user %s", claims.UserID)
	return nil
}

// validateLegacyClaims performs validation on legacy JWT claims
func (j *JWTService) validateLegacyClaims(claims *JWTClaims) error {
	now := time.Now()

	// Check expiration with detailed error message
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(now) {
		return fmt.Errorf("token has expired at %v (current time: %v)", claims.ExpiresAt.Time, now)
	}

	// Check not before with detailed error message
	if claims.NotBefore != nil && claims.NotBefore.After(now) {
		return fmt.Errorf("token not valid until %v (current time: %v)", claims.NotBefore.Time, now)
	}

	// Check issuer with detailed error message
	if claims.Issuer != j.config.JWT.Issuer {
		return fmt.Errorf("invalid issuer: expected %s, got %s", j.config.JWT.Issuer, claims.Issuer)
	}

	// Check audience with detailed error message
	if len(claims.Audience) == 0 || claims.Audience[0] != j.config.JWT.Audience {
		expectedAud := j.config.JWT.Audience
		actualAud := ""
		if len(claims.Audience) > 0 {
			actualAud = claims.Audience[0]
		}
		return fmt.Errorf("invalid audience: expected %s, got %s", expectedAud, actualAud)
	}

	// Check required fields with specific error messages
	if claims.UserID == "" {
		return fmt.Errorf("user ID is required but missing")
	}

	if claims.Email == "" {
		return fmt.Errorf("email is required but missing")
	}

	// Validate email format (basic check)
	if !strings.Contains(claims.Email, "@") {
		return fmt.Errorf("invalid email format: %s", claims.Email)
	}

	// Note: We don't validate UUID format for UserID to support Google IDs

	return nil
}

// validateClaims performs additional validation on JWT claims (legacy method)
func (j *JWTService) validateClaims(claims *JWTClaims) error {
	return j.validateLegacyClaims(claims)
}

// GetPublicKeyPEM returns the public key in PEM format for external validation
func (j *JWTService) GetPublicKeyPEM() (string, error) {
	pubKeyBytes, err := x509.MarshalPKIXPublicKey(j.publicKey)
	if err != nil {
		return "", fmt.Errorf("failed to marshal public key: %w", err)
	}

	pubKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubKeyBytes,
	})

	return string(pubKeyPEM), nil
}

// GenerateSecureSecret generates a cryptographically secure secret
func GenerateSecureSecret() (string, error) {
	bytes := make([]byte, 64) // 512 bits
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate secure random bytes: %w", err)
	}
	return base64.StdEncoding.EncodeToString(bytes), nil
}
