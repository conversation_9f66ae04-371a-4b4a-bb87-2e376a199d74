package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"carnow-backend/internal/config"
	"carnow-backend/internal/core/domain"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Note: SupabaseUser and SupabaseAuthResponse are defined in auth_handlers.go

// JWTMiddleware validates JWT tokens with enhanced validation and error handling
// Task 1: Fix JWT token generation and validation
func JWTMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			fmt.Printf("❌ JWT Middleware: No authorization header provided\n")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "AUTH_HEADER_MISSING",
				"message": "Please provide a valid authorization token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token with enhanced validation
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			fmt.Printf("❌ JWT Middleware: Invalid authorization header format: %s\n", authHeader)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "AUTH_HEADER_INVALID",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		if tokenString == "" {
			fmt.Printf("❌ JWT Middleware: Empty token provided\n")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Empty token provided",
				"code":    "TOKEN_EMPTY",
				"message": "Token cannot be empty",
			})
			c.Abort()
			return
		}

		// Try enhanced JWT validation first, then fallback to Supabase validation
		if jwtService, err := createJWTService(cfg); err == nil {
			if claims, err := jwtService.ValidateEnhancedToken(tokenString); err == nil {
				// Enhanced JWT token validated successfully
				fmt.Printf("✅ JWT Middleware: Enhanced JWT validated for user %s\n", claims.Email)

				// Set enhanced user information in context
				c.Set("user_id", claims.UserID)
				c.Set("user_email", claims.Email)
				c.Set("user_name", claims.Name)
				c.Set("user_provider", claims.Provider)
				c.Set("is_admin", claims.IsAdmin)
				c.Set("token_type", "enhanced_jwt")
				c.Next()
				return
			}
		}

		// Fallback to Supabase token validation for backward compatibility
		user, err := validateTokenWithSupabase(cfg, tokenString)
		if err != nil {
			fmt.Printf("❌ JWT Middleware: All token validation methods failed: %v\n", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid token",
				"code":    "TOKEN_VALIDATION_FAILED",
				"message": "Token validation failed. Please login again.",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Auto-create user if doesn't exist (Supabase flow)
		if err := ensureUserExists(c, user); err != nil {
			fmt.Printf("❌ JWT Middleware: Failed to ensure user exists: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to ensure user exists",
				"code":    "USER_CREATION_FAILED",
				"message": "Internal server error",
			})
			c.Abort()
			return
		}

		// Set Supabase user information in context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("token_type", "supabase")

		fmt.Printf("✅ JWT Middleware: Supabase token validated for user %s\n", user.Email)
		c.Next()
	}
}

// createJWTService creates a JWT service instance for token validation
func createJWTService(cfg *config.Config) (*services.JWTService, error) {
	// Import the services package
	return services.NewJWTService(cfg)
}

// validateTokenWithSupabase validates JWT token by calling Supabase Auth API
func validateTokenWithSupabase(cfg *config.Config, token string) (*SupabaseUser, error) {
	// Create request to Supabase Auth API
	url := fmt.Sprintf("%s/auth/v1/user", cfg.Supabase.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("apikey", cfg.Supabase.AnonKey)

	// Make request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate token: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("invalid token - status: %d", resp.StatusCode)
	}

	// Parse response
	var supabaseUser SupabaseUser
	if err := json.NewDecoder(resp.Body).Decode(&supabaseUser); err != nil {
		return nil, fmt.Errorf("failed to parse user data: %v", err)
	}

	// Set default role if not provided
	if supabaseUser.Role == "" {
		supabaseUser.Role = "authenticated"
	}

	fmt.Printf("✅ Token validated for user: %s (%s)\n", supabaseUser.Email, supabaseUser.ID)
	return &supabaseUser, nil
}

// ensureUserExists checks if user exists in Go backend, creates if not
func ensureUserExists(c *gin.Context, user *SupabaseUser) error {
	db, exists := c.Get("db")
	if !exists {
		return fmt.Errorf("database connection not found in context")
	}

	gormDB := db.(*gorm.DB)

	// Check if user exists
	var existingUser domain.User
	result := gormDB.Where("id = ?", user.ID).First(&existingUser)

	if result.Error == nil {
		// User exists, no need to create
		fmt.Printf("✅ User exists in Go backend: %s\n", user.Email)
		return nil
	}

	if result.Error != gorm.ErrRecordNotFound {
		return fmt.Errorf("database error: %v", result.Error)
	}

	// User doesn't exist, create new user
	// No need to parse UUID anymore, use string ID directly
	userID := user.ID

	// Extract username from email
	usernameStr := strings.Split(user.Email, "@")[0]

	newUser := domain.User{
		ID:       userID,
		Email:    user.Email,
		Username: &usernameStr, // Use email prefix as username
		// We don't have full_name from Supabase user endpoint, so we'll leave it empty for now
	}

	// Create user in transaction
	err := gormDB.Transaction(func(tx *gorm.DB) error {
		// Create user
		if err := tx.Create(&newUser).Error; err != nil {
			return err
		}

		// Create wallet for user
		wallet := domain.Wallet{
			UserID:  userID,
			Balance: 0.0,
		}

		if err := tx.Create(&wallet).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("failed to create user and wallet: %v", err)
	}

	fmt.Printf("✅ Auto-created user in Go backend: %s (ID: %s)\n", user.Email, user.ID)
	fmt.Printf("✅ Created wallet for user: %s\n", user.ID)

	return nil
}

// OptionalJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalJWTMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			c.Next()
			return
		}

		// If auth header exists, validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		user, err := validateTokenWithSupabase(cfg, tokenString)

		// If valid token, set user context
		if err == nil {
			// Auto-create user if doesn't exist (optional middleware)
			_ = ensureUserExists(c, user)

			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
		}

		c.Next()
	}
}
