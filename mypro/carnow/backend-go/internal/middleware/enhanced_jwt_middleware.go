package middleware

import (
	"log"
	"net/http"
	"strings"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
)

// EnhancedJWTMiddleware validates enhanced JWT tokens with comprehensive error handling
// Task 1: Implement robust JWT middleware with enhanced validation
func EnhancedJWTMiddleware(cfg *config.Config, jwtService *services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			log.Printf("❌ JWT Middleware: No authorization header provided")
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "AUTH_HEADER_MISSING",
				"message": "Please provide a valid authorization token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token with proper validation
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Printf("❌ JWT Middleware: Invalid authorization header format: %s", authHeader)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "AUTH_HEADER_INVALID",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		if tokenString == "" {
			log.Printf("❌ JWT Middleware: Empty token provided")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Empty token provided",
				"code":    "TOKEN_EMPTY",
				"message": "Token cannot be empty",
			})
			c.Abort()
			return
		}

		// Validate enhanced JWT token
		claims, err := jwtService.ValidateEnhancedToken(tokenString)
		if err != nil {
			log.Printf("❌ JWT Middleware: Enhanced token validation failed: %v", err)

			// Provide specific error responses based on error type
			errorResponse := determineTokenError(err)
			c.JSON(http.StatusUnauthorized, errorResponse)
			c.Abort()
			return
		}

		// Set enhanced user information in context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_name", claims.Name)
		c.Set("user_provider", claims.Provider)
		c.Set("is_admin", claims.IsAdmin)
		c.Set("token_id", claims.ID)
		c.Set("token_type", claims.TokenType)

		log.Printf("✅ JWT Middleware: Enhanced token validated successfully for user %s (%s)",
			claims.Email, claims.UserID)

		c.Next()
	}
}

// OptionalEnhancedJWTMiddleware allows both authenticated and non-authenticated requests
// Task 1: Provide optional authentication for endpoints that work with or without auth
func OptionalEnhancedJWTMiddleware(cfg *config.Config, jwtService *services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			c.Next()
			return
		}

		// If auth header exists, try to validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			// Invalid format, but continue without auth
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		if tokenString == "" {
			// Empty token, continue without auth
			c.Next()
			return
		}

		// Try to validate enhanced token
		claims, err := jwtService.ValidateEnhancedToken(tokenString)
		if err != nil {
			// Invalid token, but continue without auth (optional middleware)
			log.Printf("⚠️ JWT Middleware (Optional): Token validation failed, continuing without auth: %v", err)
			c.Next()
			return
		}

		// Valid token, set user context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_name", claims.Name)
		c.Set("user_provider", claims.Provider)
		c.Set("is_admin", claims.IsAdmin)
		c.Set("token_id", claims.ID)
		c.Set("token_type", claims.TokenType)

		log.Printf("✅ JWT Middleware (Optional): Enhanced token validated for user %s", claims.Email)

		c.Next()
	}
}

// determineTokenError provides specific error responses based on the validation error
// Task 1: Add comprehensive error handling and logging
func determineTokenError(err error) gin.H {
	errorMsg := err.Error()

	switch {
	case strings.Contains(errorMsg, "expired"):
		return gin.H{
			"error":   "Token has expired",
			"code":    "TOKEN_EXPIRED",
			"message": "Your session has expired. Please login again.",
		}
	case strings.Contains(errorMsg, "signature"):
		return gin.H{
			"error":   "Invalid token signature",
			"code":    "TOKEN_SIGNATURE_INVALID",
			"message": "Token signature is invalid. Please login again.",
		}
	case strings.Contains(errorMsg, "issuer"):
		return gin.H{
			"error":   "Invalid token issuer",
			"code":    "TOKEN_ISSUER_INVALID",
			"message": "Token was not issued by a trusted source.",
		}
	case strings.Contains(errorMsg, "audience"):
		return gin.H{
			"error":   "Invalid token audience",
			"code":    "TOKEN_AUDIENCE_INVALID",
			"message": "Token is not intended for this application.",
		}
	case strings.Contains(errorMsg, "format"):
		return gin.H{
			"error":   "Invalid token format",
			"code":    "TOKEN_FORMAT_INVALID",
			"message": "Token format is invalid. Please login again.",
		}
	case strings.Contains(errorMsg, "revoked"):
		return gin.H{
			"error":   "Token has been revoked",
			"code":    "TOKEN_REVOKED",
			"message": "Your session has been terminated. Please login again.",
		}
	case strings.Contains(errorMsg, "not valid yet"):
		return gin.H{
			"error":   "Token not valid yet",
			"code":    "TOKEN_NOT_VALID_YET",
			"message": "Token is not valid yet. Please check your system time.",
		}
	case strings.Contains(errorMsg, "missing"):
		return gin.H{
			"error":   "Missing required token claims",
			"code":    "TOKEN_CLAIMS_MISSING",
			"message": "Token is missing required information. Please login again.",
		}
	default:
		return gin.H{
			"error":   "Token validation failed",
			"code":    "TOKEN_VALIDATION_FAILED",
			"message": "Invalid token. Please login again.",
		}
	}
}

// LegacyJWTMiddleware validates legacy JWT tokens for backward compatibility
// Task 1: Maintain backward compatibility with existing tokens
func LegacyJWTMiddleware(cfg *config.Config, jwtService *services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "AUTH_HEADER_MISSING",
				"message": "Please provide a valid authorization token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "AUTH_HEADER_INVALID",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// Validate legacy JWT token
		claims, err := jwtService.ValidateToken(tokenString)
		if err != nil {
			log.Printf("❌ JWT Middleware (Legacy): Token validation failed: %v", err)

			errorResponse := determineTokenError(err)
			c.JSON(http.StatusUnauthorized, errorResponse)
			c.Abort()
			return
		}

		// Set legacy user information in context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("token_id", claims.ID)
		c.Set("token_type", claims.TokenType)

		log.Printf("✅ JWT Middleware (Legacy): Token validated successfully for user %s", claims.Email)

		c.Next()
	}
}
