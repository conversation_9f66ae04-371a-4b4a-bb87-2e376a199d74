package domain

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the unified schema (public.users)
// Forever Plan: Keep it simple and clean
// Updated to support string IDs for Google OAuth compatibility
type User struct {
	ID        string    `gorm:"type:varchar(255);primary_key" json:"id"`
	GoogleID  *string   `gorm:"type:varchar(255);uniqueIndex;column:google_id" json:"google_id"`
	Email     string    `gorm:"uniqueIndex;not null" json:"email"`
	FullName  *string   `gorm:"column:full_name" json:"full_name"`
	Username  *string   `gorm:"uniqueIndex" json:"username"`
	Role      string    `gorm:"default:'user'" json:"role"`
	Status    string    `gorm:"default:'active'" json:"status"`
	IsActive  bool      `gorm:"default:true" json:"is_active"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted bool      `gorm:"default:false" json:"is_deleted"`
}

// TableName returns the table name for User
func (User) TableName() string {
	return "public.users"
}

// Wallet represents a user wallet in the finance schema
// Forever Plan: Simple wallet with basic fields
// Updated to support string UserID for Google OAuth compatibility
type Wallet struct {
	ID               uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           string    `gorm:"type:varchar(255);not null;index" json:"user_id"`
	Balance          float64   `gorm:"type:decimal(15,2);default:0" json:"balance"`
	FrozenBalance    float64   `gorm:"type:decimal(15,2);default:0" json:"frozen_balance"`
	Currency         string    `gorm:"default:'LYD'" json:"currency"`
	DailyLimit       float64   `gorm:"type:decimal(15,2);default:1000" json:"daily_limit"`
	MonthlyLimit     float64   `gorm:"type:decimal(15,2);default:10000" json:"monthly_limit"`
	Status           string    `gorm:"default:'active'" json:"status"`
	IsActive         bool      `gorm:"default:true" json:"is_active"`
	LastResetDaily   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"last_reset_daily"`
	LastResetMonthly time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"last_reset_monthly"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted        bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	User User `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
}

// TableName returns the table name for Wallet
func (Wallet) TableName() string {
	return "finance.wallets"
}

// Product represents a product in the system
// Forever Plan: Updated to match existing database schema
type Product struct {
	ID             uuid.UUID              `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID         *string                `gorm:"type:varchar(255);index" json:"user_id"` // Made optional to match DB, updated for string ID
	SellerID       *uuid.UUID             `gorm:"type:uuid;index" json:"seller_id"`       // Added from DB schema
	Name           string                 `gorm:"not null" json:"name"`
	NameAr         *string                `gorm:"column:name_ar" json:"name_ar"` // Added from DB
	NameEn         *string                `gorm:"column:name_en" json:"name_en"` // Added from DB
	NameIt         *string                `gorm:"column:name_it" json:"name_it"` // Added from DB
	Description    *string                `gorm:"type:text" json:"description"`
	Price          float64                `gorm:"type:decimal(15,2);not null" json:"price"`
	OriginalPrice  *float64               `gorm:"type:decimal(15,2)" json:"original_price"` // Added from DB
	CategoryRootID *int                   `json:"category_root_id"`                         // Made optional to match DB
	CategoryID     *uuid.UUID             `gorm:"type:uuid" json:"category_id"`             // Added from DB
	SubcategoryID  *int                   `json:"subcategory_id"`                           // Added from DB
	Category       *string                `json:"category"`                                 // Added from DB
	PartID         *int                   `json:"part_id"`                                  // Added from DB
	Condition      *string                `json:"condition"`                                // Added from DB
	Brand          *string                `json:"brand"`                                    // Added from DB
	Model          *string                `json:"model"`                                    // Added from DB
	Year           *int                   `json:"year"`                                     // Added from DB
	YearFrom       *int                   `json:"year_from"`                                // Added from DB
	YearTo         *int                   `json:"year_to"`                                  // Added from DB
	PartNumber     *string                `json:"part_number"`                              // Added from DB
	Location       *string                `json:"location"`                                 // Added from DB
	ProductType    *string                `json:"product_type"`                             // Added from DB
	AutomotiveType *string                `json:"automotive_type"`                          // Added from DB
	IsFeatured     *bool                  `gorm:"default:false" json:"is_featured"`         // Added from DB
	IsAvailable    *bool                  `gorm:"default:true" json:"is_available"`         // Added from DB
	IsActive       *bool                  `gorm:"default:true" json:"is_active"`
	StockQuantity  *int                   `json:"stock_quantity"`                   // Added from DB
	ViewsCount     *int                   `gorm:"default:0" json:"views_count"`     // Added from DB
	Images         []string               `gorm:"type:text[]" json:"images"`        // Added from DB
	Specifications map[string]interface{} `gorm:"type:jsonb" json:"specifications"` // Added from DB
	CreatedAt      time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted      bool                   `gorm:"default:false" json:"is_deleted"`

	// Relations - Simplified for Forever Plan
	User   *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Seller *User `gorm:"foreignKey:SellerID" json:"seller,omitempty"`
}

// TableName returns the table name for Product
func (Product) TableName() string {
	return "public.\"Products\""
}

// WalletTransaction represents a transaction in a wallet
// Forever Plan: Simple transaction tracking
type WalletTransaction struct {
	ID            uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	WalletID      uuid.UUID  `gorm:"type:uuid;not null;index" json:"wallet_id"`
	UserID        string     `gorm:"type:varchar(255);not null;index" json:"user_id"`
	Type          string     `gorm:"not null" json:"type"` // deposit, withdraw, transfer
	Amount        float64    `gorm:"type:decimal(15,2);not null" json:"amount"`
	Currency      string     `gorm:"default:'LYD'" json:"currency"`
	BalanceBefore float64    `gorm:"type:decimal(15,2)" json:"balance_before"`
	BalanceAfter  float64    `gorm:"type:decimal(15,2)" json:"balance_after"`
	Description   *string    `gorm:"type:text" json:"description"`
	ReferenceID   *string    `gorm:"index" json:"reference_id"`
	Status        string     `gorm:"default:'completed'" json:"status"`
	ProcessedAt   *time.Time `gorm:"" json:"processed_at"`
	CreatedAt     time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted     bool       `gorm:"default:false" json:"is_deleted"`

	// Relations
	Wallet Wallet `gorm:"foreignKey:WalletID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"wallet,omitempty"`
	User   User   `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
}

// TableName returns the table name for WalletTransaction
func (WalletTransaction) TableName() string {
	return "finance.wallet_transactions"
}

// FinancialOperation represents a financial operation
// Forever Plan: Simple financial operations
type FinancialOperation struct {
	ID          uuid.UUID              `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      string                 `gorm:"type:varchar(255);not null;index" json:"user_id"`
	Type        string                 `gorm:"not null" json:"type"` // payment, refund, transfer
	Amount      float64                `gorm:"type:decimal(15,2);not null" json:"amount"`
	Currency    string                 `gorm:"default:'LYD'" json:"currency"`
	Status      string                 `gorm:"default:'pending'" json:"status"`
	Description *string                `gorm:"type:text" json:"description"`
	Gateway     *string                `gorm:"" json:"gateway"`
	Metadata    map[string]interface{} `gorm:"type:jsonb" json:"metadata"`
	ProcessedAt *time.Time             `gorm:"" json:"processed_at"`
	CreatedAt   time.Time              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time              `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted   bool                   `gorm:"default:false" json:"is_deleted"`

	// Relations
	User User `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
}

// TableName returns the table name for FinancialOperation
func (FinancialOperation) TableName() string {
	return "finance.financial_operations"
}

// Constants for model enums
const (
	// User roles
	UserRoleUser     = "user"
	UserRoleAdmin    = "admin"
	UserRoleCustomer = "customer"

	// User status
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"

	// Wallet status
	WalletStatusActive   = "active"
	WalletStatusFrozen   = "frozen"
	WalletStatusInactive = "inactive"

	// Transaction types
	TransactionTypeDeposit  = "deposit"
	TransactionTypeWithdraw = "withdraw"
	TransactionTypeTransfer = "transfer"

	// Transaction status
	TransactionStatusPending   = "pending"
	TransactionStatusCompleted = "completed"
	TransactionStatusFailed    = "failed"
	TransactionStatusCancelled = "cancelled"

	// Financial operation types
	OperationTypePayment    = "payment"
	OperationTypeRefund     = "refund"
	OperationTypeTransfer   = "transfer"
	OperationTypeWithdrawal = "withdrawal"
	OperationTypeDeposit    = "deposit"

	// Financial operation status
	OperationStatusPending   = "pending"
	OperationStatusCompleted = "completed"
	OperationStatusFailed    = "failed"
	OperationStatusCancelled = "cancelled"

	// Currency
	CurrencyLYD = "LYD"
	CurrencyUSD = "USD"
)

// Request/Response DTOs for API
// Forever Plan: Keep them simple and clean

type CreateUserRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	FullName *string `json:"full_name"`
	Username *string `json:"username"`
}

type UpdateUserRequest struct {
	FullName *string `json:"full_name"`
	Username *string `json:"username"`
}

type CreateProductRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Price       float64 `json:"price" binding:"required,min=0"`
}

type UpdateWalletRequest struct {
	Amount float64 `json:"amount" binding:"required"`
	Action string  `json:"action" binding:"required,oneof=add subtract"`
}

type CreateTransactionRequest struct {
	Type        string  `json:"type" binding:"required,oneof=deposit withdraw transfer"`
	Amount      float64 `json:"amount" binding:"required,min=0"`
	Description *string `json:"description"`
}

// BeforeCreate hook for setting defaults
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// For string IDs, we don't auto-generate them
	// Google OAuth users will have their Google ID set as the primary key
	// Other users can have UUIDs converted to strings if needed
	if u.ID == "" {
		// Generate a UUID and convert to string for non-Google users
		u.ID = uuid.New().String()
	}
	return nil
}

func (w *Wallet) BeforeCreate(tx *gorm.DB) error {
	if w.ID == uuid.Nil {
		w.ID = uuid.New()
	}
	now := time.Now()
	w.LastResetDaily = now
	w.LastResetMonthly = now
	return nil
}

func (p *Product) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

func (wt *WalletTransaction) BeforeCreate(tx *gorm.DB) error {
	if wt.ID == uuid.Nil {
		wt.ID = uuid.New()
	}
	now := time.Now()
	wt.ProcessedAt = &now
	return nil
}

func (fo *FinancialOperation) BeforeCreate(tx *gorm.DB) error {
	if fo.ID == uuid.Nil {
		fo.ID = uuid.New()
	}
	return nil
}

// Order represents an order in the system
// Forever Plan: Simple order model
type Order struct {
	ID              uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          string    `gorm:"type:varchar(255);not null;index" json:"user_id"`
	OrderNumber     string    `gorm:"uniqueIndex;not null" json:"order_number"`
	Status          string    `gorm:"default:'pending'" json:"status"`
	TotalAmount     float64   `gorm:"type:decimal(15,2);not null" json:"total_amount"`
	Currency        string    `gorm:"default:'LYD'" json:"currency"`
	ShippingAddress *string   `gorm:"type:text" json:"shipping_address"`
	BillingAddress  *string   `gorm:"type:text" json:"billing_address"`
	PaymentMethod   *string   `json:"payment_method"`
	PaymentStatus   string    `gorm:"default:'pending'" json:"payment_status"`
	Notes           *string   `gorm:"type:text" json:"notes"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted       bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	User       User        `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
	OrderItems []OrderItem `gorm:"foreignKey:OrderID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"order_items,omitempty"`
}

// TableName returns the table name for Order
func (Order) TableName() string {
	return "public.orders"
}

// OrderItem represents an item within an order
// Forever Plan: Simple order item model
type OrderItem struct {
	ID         uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	OrderID    uuid.UUID `gorm:"type:uuid;not null;index" json:"order_id"`
	ProductID  uuid.UUID `gorm:"type:uuid;not null;index" json:"product_id"`
	Quantity   int       `gorm:"not null;default:1" json:"quantity"`
	UnitPrice  float64   `gorm:"type:decimal(15,2);not null" json:"unit_price"`
	TotalPrice float64   `gorm:"type:decimal(15,2);not null" json:"total_price"`
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted  bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	Order   Order   `gorm:"foreignKey:OrderID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"order,omitempty"`
	Product Product `gorm:"foreignKey:ProductID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"product,omitempty"`
}

// TableName returns the table name for OrderItem
func (OrderItem) TableName() string {
	return "public.order_items"
}

// Constants for Order system
const (
	// Order status
	OrderStatusPending    = "pending"
	OrderStatusConfirmed  = "confirmed"
	OrderStatusProcessing = "processing"
	OrderStatusShipped    = "shipped"
	OrderStatusDelivered  = "delivered"
	OrderStatusCancelled  = "cancelled"
	OrderStatusRefunded   = "refunded"

	// Payment status
	PaymentStatusPending   = "pending"
	PaymentStatusCompleted = "completed"
	PaymentStatusFailed    = "failed"
	PaymentStatusRefunded  = "refunded"
	PaymentStatusPartial   = "partial"

	// Payment methods
	PaymentMethodCash         = "cash"
	PaymentMethodCard         = "card"
	PaymentMethodWallet       = "wallet"
	PaymentMethodBankTransfer = "bank_transfer"
)

// Request/Response DTOs for Orders API
type CreateOrderRequest struct {
	ShippingAddress *string                  `json:"shipping_address"`
	BillingAddress  *string                  `json:"billing_address"`
	PaymentMethod   *string                  `json:"payment_method"`
	Notes           *string                  `json:"notes"`
	Items           []CreateOrderItemRequest `json:"items" binding:"required,min=1"`
}

type CreateOrderItemRequest struct {
	ProductID uuid.UUID `json:"product_id" binding:"required"`
	Quantity  int       `json:"quantity" binding:"required,min=1"`
}

type UpdateOrderRequest struct {
	Status          *string `json:"status"`
	ShippingAddress *string `json:"shipping_address"`
	BillingAddress  *string `json:"billing_address"`
	PaymentMethod   *string `json:"payment_method"`
	PaymentStatus   *string `json:"payment_status"`
	Notes           *string `json:"notes"`
}

// BeforeCreate hook for Order
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}
	// Generate order number if not set
	if o.OrderNumber == "" {
		o.OrderNumber = fmt.Sprintf("ORD-%d-%s", time.Now().Unix(), o.ID.String()[:8])
	}
	return nil
}

// BeforeCreate hook for OrderItem
func (oi *OrderItem) BeforeCreate(tx *gorm.DB) error {
	if oi.ID == uuid.Nil {
		oi.ID = uuid.New()
	}
	// Calculate total price
	oi.TotalPrice = oi.UnitPrice * float64(oi.Quantity)
	return nil
}

// Seller represents a seller/store owner in the system
// Forever Plan: Simple seller model
type Seller struct {
	ID              uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          string    `gorm:"type:varchar(255);not null;index" json:"user_id"`
	BusinessName    string    `gorm:"not null" json:"business_name"`
	BusinessType    string    `gorm:"default:'individual'" json:"business_type"`
	TaxNumber       *string   `json:"tax_number"`
	BusinessAddress *string   `gorm:"type:text" json:"business_address"`
	PhoneNumber     *string   `json:"phone_number"`
	Email           *string   `json:"email"`
	Website         *string   `json:"website"`
	Description     *string   `gorm:"type:text" json:"description"`
	Status          string    `gorm:"default:'pending'" json:"status"`
	IsVerified      bool      `gorm:"default:false" json:"is_verified"`
	IsActive        bool      `gorm:"default:true" json:"is_active"`
	CommissionRate  float64   `gorm:"type:decimal(5,2);default:5.00" json:"commission_rate"`
	Rating          float64   `gorm:"type:decimal(3,2);default:0" json:"rating"`
	TotalSales      int       `gorm:"default:0" json:"total_sales"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted       bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	User     User      `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
	Products []Product `gorm:"foreignKey:UserID;references:UserID" json:"products,omitempty"`
}

// TableName returns the table name for Seller
func (Seller) TableName() string {
	return "public.sellers"
}

// SellerDocument represents seller verification documents
// Forever Plan: Simple document tracking
type SellerDocument struct {
	ID           uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	SellerID     uuid.UUID  `gorm:"type:uuid;not null;index" json:"seller_id"`
	DocumentType string     `gorm:"not null" json:"document_type"`
	DocumentURL  string     `gorm:"not null" json:"document_url"`
	Status       string     `gorm:"default:'pending'" json:"status"`
	UploadedAt   time.Time  `gorm:"autoCreateTime" json:"uploaded_at"`
	VerifiedAt   *time.Time `json:"verified_at"`
	Notes        *string    `gorm:"type:text" json:"notes"`
	IsDeleted    bool       `gorm:"default:false" json:"is_deleted"`

	// Relations
	Seller Seller `gorm:"foreignKey:SellerID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"seller,omitempty"`
}

// TableName returns the table name for SellerDocument
func (SellerDocument) TableName() string {
	return "public.seller_documents"
}

// Constants for Seller system
const (
	// Seller status
	SellerStatusPending   = "pending"
	SellerStatusApproved  = "approved"
	SellerStatusRejected  = "rejected"
	SellerStatusSuspended = "suspended"
	SellerStatusActive    = "active"

	// Business types
	BusinessTypeIndividual  = "individual"
	BusinessTypeCompany     = "company"
	BusinessTypePartnership = "partnership"
	BusinessTypeCooperative = "cooperative"

	// Document types
	DocumentTypeID            = "id_card"
	DocumentTypePassport      = "passport"
	DocumentTypeLicense       = "business_license"
	DocumentTypeTaxCard       = "tax_card"
	DocumentTypeBankStatement = "bank_statement"
	DocumentTypeOther         = "other"

	// Document status
	DocumentStatusPending  = "pending"
	DocumentStatusApproved = "approved"
	DocumentStatusRejected = "rejected"
)

// Request/Response DTOs for Seller API
type CreateSellerRequest struct {
	BusinessName    string  `json:"business_name" binding:"required"`
	BusinessType    string  `json:"business_type" binding:"required"`
	TaxNumber       *string `json:"tax_number"`
	BusinessAddress *string `json:"business_address"`
	PhoneNumber     *string `json:"phone_number"`
	Email           *string `json:"email"`
	Website         *string `json:"website"`
	Description     *string `json:"description"`
}

type UpdateSellerRequest struct {
	BusinessName    *string  `json:"business_name"`
	BusinessType    *string  `json:"business_type"`
	TaxNumber       *string  `json:"tax_number"`
	BusinessAddress *string  `json:"business_address"`
	PhoneNumber     *string  `json:"phone_number"`
	Email           *string  `json:"email"`
	Website         *string  `json:"website"`
	Description     *string  `json:"description"`
	CommissionRate  *float64 `json:"commission_rate"`
}

type UpdateSellerStatusRequest struct {
	Status     string  `json:"status" binding:"required"`
	IsVerified *bool   `json:"is_verified"`
	Notes      *string `json:"notes"`
}

type UploadDocumentRequest struct {
	DocumentType string `json:"document_type" binding:"required"`
	DocumentURL  string `json:"document_url" binding:"required"`
}

// BeforeCreate hook for Seller
func (s *Seller) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for SellerDocument
func (sd *SellerDocument) BeforeCreate(tx *gorm.DB) error {
	if sd.ID == uuid.Nil {
		sd.ID = uuid.New()
	}
	return nil
}

// Category represents a product category in the system
// Forever Plan: Simple hierarchical category model
type Category struct {
	ID           uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Name         string     `gorm:"not null" json:"name"`
	Slug         string     `gorm:"uniqueIndex;not null" json:"slug"`
	Description  *string    `gorm:"type:text" json:"description"`
	ImageURL     *string    `json:"image_url"`
	ParentID     *uuid.UUID `gorm:"type:uuid;index" json:"parent_id"`
	Level        int        `gorm:"default:0" json:"level"`
	SortOrder    int        `gorm:"default:0" json:"sort_order"`
	IsActive     bool       `gorm:"default:true" json:"is_active"`
	ProductCount int        `gorm:"default:0" json:"product_count"`
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted    bool       `gorm:"default:false" json:"is_deleted"`

	// Relations
	Parent   *Category  `gorm:"foreignKey:ParentID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"parent,omitempty"`
	Children []Category `gorm:"foreignKey:ParentID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"children,omitempty"`
	Products []Product  `gorm:"many2many:product_categories;" json:"products,omitempty"`
}

// TableName returns the table name for Category
func (Category) TableName() string {
	return "public.categories"
}

// ProductCategory represents the many-to-many relationship between products and categories
// Forever Plan: Simple junction table
type ProductCategory struct {
	ID         uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ProductID  uuid.UUID `gorm:"type:uuid;not null;index" json:"product_id"`
	CategoryID uuid.UUID `gorm:"type:uuid;not null;index" json:"category_id"`
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`

	// Relations
	Product  Product  `gorm:"foreignKey:ProductID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"product,omitempty"`
	Category Category `gorm:"foreignKey:CategoryID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"category,omitempty"`
}

// TableName returns the table name for ProductCategory
func (ProductCategory) TableName() string {
	return "public.product_categories"
}

// Constants for Category system
const (
	// Category levels
	CategoryLevelRoot  = 0
	CategoryLevelOne   = 1
	CategoryLevelTwo   = 2
	CategoryLevelThree = 3
	CategoryLevelFour  = 4
)

// Request/Response DTOs for Categories API
type CreateCategoryRequest struct {
	Name        string     `json:"name" binding:"required"`
	Slug        string     `json:"slug" binding:"required"`
	Description *string    `json:"description"`
	ImageURL    *string    `json:"image_url"`
	ParentID    *uuid.UUID `json:"parent_id"`
	SortOrder   *int       `json:"sort_order"`
}

type UpdateCategoryRequest struct {
	Name        *string    `json:"name"`
	Slug        *string    `json:"slug"`
	Description *string    `json:"description"`
	ImageURL    *string    `json:"image_url"`
	ParentID    *uuid.UUID `json:"parent_id"`
	SortOrder   *int       `json:"sort_order"`
	IsActive    *bool      `json:"is_active"`
}

type CategoryTreeResponse struct {
	ID           uuid.UUID              `json:"id"`
	Name         string                 `json:"name"`
	Slug         string                 `json:"slug"`
	Description  *string                `json:"description"`
	ImageURL     *string                `json:"image_url"`
	Level        int                    `json:"level"`
	SortOrder    int                    `json:"sort_order"`
	ProductCount int                    `json:"product_count"`
	Children     []CategoryTreeResponse `json:"children,omitempty"`
}

// BeforeCreate hook for Category
func (c *Category) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}

	// Calculate level based on parent
	if c.ParentID != nil {
		var parent Category
		if err := tx.Where("id = ?", *c.ParentID).First(&parent).Error; err != nil {
			return err
		}
		c.Level = parent.Level + 1
	} else {
		c.Level = CategoryLevelRoot
	}

	return nil
}

// BeforeCreate hook for ProductCategory
func (pc *ProductCategory) BeforeCreate(tx *gorm.DB) error {
	if pc.ID == uuid.Nil {
		pc.ID = uuid.New()
	}
	return nil
}

// Vehicle Brand represents car brands/manufacturers
// Forever Plan: Simple vehicle data structure
type VehicleBrand struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Name        string    `gorm:"not null;uniqueIndex" json:"name"`
	Slug        string    `gorm:"not null;uniqueIndex" json:"slug"`
	LogoURL     *string   `json:"logo_url"`
	Country     *string   `json:"country"`
	Website     *string   `json:"website"`
	Description *string   `gorm:"type:text" json:"description"`
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted   bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	Models []VehicleModel `gorm:"foreignKey:BrandID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"models,omitempty"`
}

// TableName returns the table name for VehicleBrand
func (VehicleBrand) TableName() string {
	return "public.vehicle_brands"
}

// Vehicle Model represents car models within brands
// Forever Plan: Simple model data structure
type VehicleModel struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	BrandID     uuid.UUID `gorm:"type:uuid;not null;index" json:"brand_id"`
	Name        string    `gorm:"not null" json:"name"`
	Slug        string    `gorm:"not null" json:"slug"`
	Type        string    `gorm:"default:'car'" json:"type"`
	BodyStyle   *string   `json:"body_style"`
	FuelType    *string   `json:"fuel_type"`
	ImageURL    *string   `json:"image_url"`
	Description *string   `gorm:"type:text" json:"description"`
	StartYear   *int      `json:"start_year"`
	EndYear     *int      `json:"end_year"`
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted   bool      `gorm:"default:false" json:"is_deleted"`

	// Relations
	Brand VehicleBrand `gorm:"foreignKey:BrandID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"brand,omitempty"`
}

// TableName returns the table name for VehicleModel
func (VehicleModel) TableName() string {
	return "public.vehicle_models"
}

// Vehicle represents a specific vehicle (car/truck/etc.) owned by a user
// Forever Plan: Simple vehicle tracking
type Vehicle struct {
	ID               uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           string     `gorm:"type:varchar(255);not null;index" json:"user_id"`
	BrandID          uuid.UUID  `gorm:"type:uuid;not null;index" json:"brand_id"`
	ModelID          uuid.UUID  `gorm:"type:uuid;not null;index" json:"model_id"`
	Year             int        `gorm:"not null" json:"year"`
	Color            *string    `json:"color"`
	VIN              *string    `gorm:"uniqueIndex" json:"vin"`
	LicensePlate     *string    `json:"license_plate"`
	Mileage          *int       `json:"mileage"`
	TransmissionType *string    `json:"transmission_type"`
	EngineSize       *string    `json:"engine_size"`
	FuelType         *string    `json:"fuel_type"`
	Condition        string     `gorm:"default:'good'" json:"condition"`
	PurchaseDate     *time.Time `json:"purchase_date"`
	PurchasePrice    *float64   `gorm:"type:decimal(15,2)" json:"purchase_price"`
	CurrentValue     *float64   `gorm:"type:decimal(15,2)" json:"current_value"`
	InsuranceExpiry  *time.Time `json:"insurance_expiry"`
	MaintenanceDate  *time.Time `json:"last_maintenance_date"`
	Notes            *string    `gorm:"type:text" json:"notes"`
	ImageURLs        []string   `gorm:"type:text[]" json:"image_urls"`
	IsActive         bool       `gorm:"default:true" json:"is_active"`
	CreatedAt        time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	IsDeleted        bool       `gorm:"default:false" json:"is_deleted"`

	// Relations
	User  User         `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
	Brand VehicleBrand `gorm:"foreignKey:BrandID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"brand,omitempty"`
	Model VehicleModel `gorm:"foreignKey:ModelID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"model,omitempty"`
}

// TableName returns the table name for Vehicle
func (Vehicle) TableName() string {
	return "public.vehicles"
}

// Constants for Vehicle system
const (
	// Vehicle types
	VehicleTypeCar        = "car"
	VehicleTypeTruck      = "truck"
	VehicleTypeMotorcycle = "motorcycle"
	VehicleTypeBus        = "bus"
	VehicleTypeVan        = "van"
	VehicleTypeSUV        = "suv"

	// Body styles
	BodyStyleSedan       = "sedan"
	BodyStyleHatchback   = "hatchback"
	BodyStyleCoupe       = "coupe"
	BodyStyleWagon       = "wagon"
	BodyStyleConvertible = "convertible"
	BodyStyleSUV         = "suv"
	BodyStylePickup      = "pickup"
	BodyStyleVan         = "van"

	// Fuel types
	FuelTypeGasoline = "gasoline"
	FuelTypeDiesel   = "diesel"
	FuelTypeElectric = "electric"
	FuelTypeHybrid   = "hybrid"
	FuelTypeLPG      = "lpg"
	FuelTypeCNG      = "cng"

	// Transmission types
	TransmissionManual    = "manual"
	TransmissionAutomatic = "automatic"
	TransmissionCVT       = "cvt"

	// Vehicle conditions
	ConditionExcellent = "excellent"
	ConditionGood      = "good"
	ConditionFair      = "fair"
	ConditionPoor      = "poor"
	ConditionDamaged   = "damaged"
)

// Request/Response DTOs for Vehicle API
type CreateVehicleBrandRequest struct {
	Name        string  `json:"name" binding:"required"`
	Slug        string  `json:"slug" binding:"required"`
	LogoURL     *string `json:"logo_url"`
	Country     *string `json:"country"`
	Website     *string `json:"website"`
	Description *string `json:"description"`
}

type UpdateVehicleBrandRequest struct {
	Name        *string `json:"name"`
	Slug        *string `json:"slug"`
	LogoURL     *string `json:"logo_url"`
	Country     *string `json:"country"`
	Website     *string `json:"website"`
	Description *string `json:"description"`
	IsActive    *bool   `json:"is_active"`
}

type CreateVehicleModelRequest struct {
	BrandID     uuid.UUID `json:"brand_id" binding:"required"`
	Name        string    `json:"name" binding:"required"`
	Slug        string    `json:"slug" binding:"required"`
	Type        string    `json:"type" binding:"required"`
	BodyStyle   *string   `json:"body_style"`
	FuelType    *string   `json:"fuel_type"`
	ImageURL    *string   `json:"image_url"`
	Description *string   `json:"description"`
	StartYear   *int      `json:"start_year"`
	EndYear     *int      `json:"end_year"`
}

type UpdateVehicleModelRequest struct {
	Name        *string `json:"name"`
	Slug        *string `json:"slug"`
	Type        *string `json:"type"`
	BodyStyle   *string `json:"body_style"`
	FuelType    *string `json:"fuel_type"`
	ImageURL    *string `json:"image_url"`
	Description *string `json:"description"`
	StartYear   *int    `json:"start_year"`
	EndYear     *int    `json:"end_year"`
	IsActive    *bool   `json:"is_active"`
}

type CreateVehicleRequest struct {
	BrandID          uuid.UUID  `json:"brand_id" binding:"required"`
	ModelID          uuid.UUID  `json:"model_id" binding:"required"`
	Year             int        `json:"year" binding:"required"`
	Color            *string    `json:"color"`
	VIN              *string    `json:"vin"`
	LicensePlate     *string    `json:"license_plate"`
	Mileage          *int       `json:"mileage"`
	TransmissionType *string    `json:"transmission_type"`
	EngineSize       *string    `json:"engine_size"`
	FuelType         *string    `json:"fuel_type"`
	Condition        *string    `json:"condition"`
	PurchaseDate     *time.Time `json:"purchase_date"`
	PurchasePrice    *float64   `json:"purchase_price"`
	CurrentValue     *float64   `json:"current_value"`
	InsuranceExpiry  *time.Time `json:"insurance_expiry"`
	MaintenanceDate  *time.Time `json:"last_maintenance_date"`
	Notes            *string    `json:"notes"`
	ImageURLs        []string   `json:"image_urls"`
}

type UpdateVehicleRequest struct {
	Color            *string    `json:"color"`
	VIN              *string    `json:"vin"`
	LicensePlate     *string    `json:"license_plate"`
	Mileage          *int       `json:"mileage"`
	TransmissionType *string    `json:"transmission_type"`
	EngineSize       *string    `json:"engine_size"`
	FuelType         *string    `json:"fuel_type"`
	Condition        *string    `json:"condition"`
	PurchaseDate     *time.Time `json:"purchase_date"`
	PurchasePrice    *float64   `json:"purchase_price"`
	CurrentValue     *float64   `json:"current_value"`
	InsuranceExpiry  *time.Time `json:"insurance_expiry"`
	MaintenanceDate  *time.Time `json:"last_maintenance_date"`
	Notes            *string    `json:"notes"`
	ImageURLs        []string   `json:"image_urls"`
}

// BeforeCreate hook for VehicleBrand
func (vb *VehicleBrand) BeforeCreate(tx *gorm.DB) error {
	if vb.ID == uuid.Nil {
		vb.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for VehicleModel
func (vm *VehicleModel) BeforeCreate(tx *gorm.DB) error {
	if vm.ID == uuid.Nil {
		vm.ID = uuid.New()
	}
	return nil
}

// BeforeCreate hook for Vehicle
func (v *Vehicle) BeforeCreate(tx *gorm.DB) error {
	if v.ID == uuid.Nil {
		v.ID = uuid.New()
	}
	return nil
}
