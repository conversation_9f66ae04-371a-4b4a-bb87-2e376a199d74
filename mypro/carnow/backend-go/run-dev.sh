#!/bin/bash

# =============================================================================
# CarNow Backend - Development Environment Startup Script
# =============================================================================
# 
# This script starts the CarNow Go backend in DEVELOPMENT mode
# - Configured for local development
# - Accessible from Android Emulator via ********:8080
# - Hot reload enabled
# - Debug logging enabled
# - Development database settings
#
# Usage: ./run-dev.sh
#

set -e  # Exit on any error

echo "🚀 Starting CarNow Backend - DEVELOPMENT MODE"
echo "================================================"

# Development Environment Variables
export CARNOW_APP_ENVIRONMENT=development
export CARNOW_APP_DEBUG=true

# Server Configuration for Development
export CARNOW_SERVER_HOST=0.0.0.0  # Listen on all interfaces for emulator access
export PORT=8080                    # Development port

# Database Configuration (Development)
export CARNOW_DATABASE_PASSWORD=9uS2LhnExynEJNWR
export CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
export CARNOW_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64

# Google OAuth Configuration (Development)
export CARNOW_GOOGLE_CLIENT_ID=630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com
export CARNOW_GOOGLE_CLIENT_SECRET=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
export CARNOW_GOOGLE_ANDROID_CLIENT_ID=630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com

# JWT Configuration (Development - Less Secure)
export CARNOW_JWT_SECRET=dev-jwt-secret-key-for-development-only
export CARNOW_SECURITY_ENCRYPTION_KEY=dev-encryption-key-for-development

# Development Features
export CARNOW_REDIS_ENABLED=false           # Disable Redis in development
export CARNOW_ENABLE_CORS=true              # Enable CORS for development
export CARNOW_LOG_LEVEL=debug               # Verbose logging

echo "✅ Development environment variables loaded"
echo ""
echo "🔧 Development Configuration:"
echo "   Environment: development"
echo "   Host: 0.0.0.0 (accessible from emulator)"
echo "   Port: 8080"
echo "   Debug: enabled"
echo "   Redis: disabled"
echo "   CORS: enabled"
echo ""

# Check if port is already in use
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Port 8080 is already in use. Stopping existing process..."
    lsof -ti:8080 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo "🌐 Starting development server..."
echo "📱 Android Emulator can access via: http://********:8080"
echo "💻 Local browser can access via: http://localhost:8080"
echo ""
echo "🔄 Hot reload enabled - server will restart on code changes"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start the server with hot reload using air (if available) or go run
if command -v air &> /dev/null; then
    echo "🔥 Using Air for hot reload..."
    air
else
    echo "📦 Using go run (install 'air' for hot reload: go install github.com/cosmtrek/air@latest)"
    go run cmd/main.go
fi
