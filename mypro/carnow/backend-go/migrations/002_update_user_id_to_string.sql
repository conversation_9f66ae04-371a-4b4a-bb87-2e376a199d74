-- Migration: Update User ID fields to support string IDs for Google OAuth
-- Created: 2025-07-31
-- Description: Updates user ID fields from UUID to VARCHAR to support Google OAuth string IDs

-- =============================================================================
-- BACKUP EXISTING DATA (Optional - for safety)
-- =============================================================================

-- Create backup tables before making changes
CREATE TABLE IF NOT EXISTS backup_users AS SELECT * FROM public.users;
CREATE TABLE IF NOT EXISTS backup_wallets AS SELECT * FROM wallets;
CREATE TABLE IF NOT EXISTS backup_products AS SELECT * FROM products;
CREATE TABLE IF NOT EXISTS backup_transactions AS SELECT * FROM transactions;
CREATE TABLE IF NOT EXISTS backup_financial_operations AS SELECT * FROM financial_operations;
CREATE TABLE IF NOT EXISTS backup_orders AS SELECT * FROM orders;
CREATE TABLE IF NOT EXISTS backup_sellers AS SELECT * FROM sellers;
CREATE TABLE IF NOT EXISTS backup_vehicles AS SELECT * FROM vehicles;

-- =============================================================================
-- UPDATE USERS TABLE
-- =============================================================================

-- First, create the new users table with string ID
CREATE TABLE IF NOT EXISTS public.users_new (
    id VARCHAR(255) PRIMARY KEY,
    google_id VARCHAR(255) UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    username VARCHAR(255) UNIQUE,
    role VARCHAR(50) DEFAULT 'user',
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT false
);

-- Create indexes for the new users table
CREATE INDEX IF NOT EXISTS idx_users_new_email ON public.users_new(email);
CREATE INDEX IF NOT EXISTS idx_users_new_google_id ON public.users_new(google_id);
CREATE INDEX IF NOT EXISTS idx_users_new_username ON public.users_new(username);
CREATE INDEX IF NOT EXISTS idx_users_new_status ON public.users_new(status);
CREATE INDEX IF NOT EXISTS idx_users_new_is_active ON public.users_new(is_active);

-- Migrate existing data (convert UUID to string)
INSERT INTO public.users_new (id, email, full_name, username, role, status, is_active, created_at, updated_at, is_deleted)
SELECT 
    id::text,
    email,
    full_name,
    username,
    role,
    status,
    is_active,
    created_at,
    updated_at,
    is_deleted
FROM public.users
ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- UPDATE RELATED TABLES
-- =============================================================================

-- Update wallets table (handle RLS policies)
DO $$
BEGIN
    -- Temporarily disable RLS policies that depend on user_id column
    DROP POLICY IF EXISTS "Users can insert own wallet transactions" ON wallet_transactions;
    DROP POLICY IF EXISTS "Users can insert own withdrawal requests" ON withdrawal_requests;
    DROP POLICY IF EXISTS "Users can update own wallet" ON wallets;
    DROP POLICY IF EXISTS "Users can view own wallet" ON wallets;
    DROP POLICY IF EXISTS "Users can view own wallet statistics" ON wallet_statistics;
    DROP POLICY IF EXISTS "Users can view own wallet transactions" ON wallet_transactions;
    DROP POLICY IF EXISTS "Users can view own withdrawal requests" ON withdrawal_requests;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignore errors if policies don't exist
        NULL;
END $$;

-- Now update the wallets table
ALTER TABLE wallets ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE wallets SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE wallets DROP CONSTRAINT IF EXISTS wallets_user_id_fkey;
ALTER TABLE wallets DROP COLUMN IF EXISTS user_id CASCADE;
ALTER TABLE wallets RENAME COLUMN user_id_new TO user_id;
ALTER TABLE wallets ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);

-- Update products table (user_id is nullable)
ALTER TABLE products ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE products SET user_id_new = user_id::text WHERE user_id IS NOT NULL AND user_id_new IS NULL;
ALTER TABLE products DROP CONSTRAINT IF EXISTS products_user_id_fkey;
ALTER TABLE products DROP COLUMN IF EXISTS user_id;
ALTER TABLE products RENAME COLUMN user_id_new TO user_id;
CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);

-- Update transactions table
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE transactions SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_user_id_fkey;
ALTER TABLE transactions DROP COLUMN IF EXISTS user_id;
ALTER TABLE transactions RENAME COLUMN user_id_new TO user_id;
ALTER TABLE transactions ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);

-- Update financial_operations table
ALTER TABLE financial_operations ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE financial_operations SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE financial_operations DROP CONSTRAINT IF EXISTS financial_operations_user_id_fkey;
ALTER TABLE financial_operations DROP COLUMN IF EXISTS user_id;
ALTER TABLE financial_operations RENAME COLUMN user_id_new TO user_id;
ALTER TABLE financial_operations ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_financial_operations_user_id ON financial_operations(user_id);

-- Update orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE orders SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_user_id_fkey;
ALTER TABLE orders DROP COLUMN IF EXISTS user_id;
ALTER TABLE orders RENAME COLUMN user_id_new TO user_id;
ALTER TABLE orders ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);

-- Update sellers table
ALTER TABLE sellers ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE sellers SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE sellers DROP CONSTRAINT IF EXISTS sellers_user_id_fkey;
ALTER TABLE sellers DROP COLUMN IF EXISTS user_id;
ALTER TABLE sellers RENAME COLUMN user_id_new TO user_id;
ALTER TABLE sellers ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sellers_user_id ON sellers(user_id);

-- Update vehicles table
ALTER TABLE vehicles ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
UPDATE vehicles SET user_id_new = user_id::text WHERE user_id_new IS NULL;
ALTER TABLE vehicles DROP CONSTRAINT IF EXISTS vehicles_user_id_fkey;
ALTER TABLE vehicles DROP COLUMN IF EXISTS user_id;
ALTER TABLE vehicles RENAME COLUMN user_id_new TO user_id;
ALTER TABLE vehicles ALTER COLUMN user_id SET NOT NULL;
CREATE INDEX IF NOT EXISTS idx_vehicles_user_id ON vehicles(user_id);

-- =============================================================================
-- REPLACE OLD USERS TABLE
-- =============================================================================

-- Drop the old users table and rename the new one
DROP TABLE IF EXISTS public.users;
ALTER TABLE public.users_new RENAME TO users;

-- =============================================================================
-- ADD FOREIGN KEY CONSTRAINTS
-- =============================================================================

-- Add foreign key constraints back (optional, depending on your needs)
-- Note: These are commented out because Supabase auth.users might not be directly referenceable
-- ALTER TABLE wallets ADD CONSTRAINT wallets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
-- ALTER TABLE products ADD CONSTRAINT products_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;
-- ALTER TABLE transactions ADD CONSTRAINT transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
-- ALTER TABLE financial_operations ADD CONSTRAINT financial_operations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
-- ALTER TABLE orders ADD CONSTRAINT orders_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
-- ALTER TABLE sellers ADD CONSTRAINT sellers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
-- ALTER TABLE vehicles ADD CONSTRAINT vehicles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- =============================================================================
-- UPDATE TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Create or update the updated_at trigger for users table
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE public.users IS 'Users table updated to support string IDs for Google OAuth compatibility';
COMMENT ON COLUMN public.users.id IS 'Primary key - supports both UUID strings and Google OAuth IDs';
COMMENT ON COLUMN public.users.google_id IS 'Google OAuth user ID for authentication';

-- =============================================================================
-- VERIFICATION QUERIES (Optional - for testing)
-- =============================================================================

-- Verify the migration worked correctly
-- SELECT 'users' as table_name, count(*) as record_count FROM public.users
-- UNION ALL
-- SELECT 'wallets', count(*) FROM wallets
-- UNION ALL  
-- SELECT 'products', count(*) FROM products
-- UNION ALL
-- SELECT 'transactions', count(*) FROM transactions
-- UNION ALL
-- SELECT 'financial_operations', count(*) FROM financial_operations
-- UNION ALL
-- SELECT 'orders', count(*) FROM orders
-- UNION ALL
-- SELECT 'sellers', count(*) FROM sellers
-- UNION ALL
-- SELECT 'vehicles', count(*) FROM vehicles;
