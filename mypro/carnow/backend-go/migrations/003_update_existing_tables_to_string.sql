-- Migration: Update existing tables to support string user IDs
-- Created: 2025-07-31
-- Description: Updates only existing tables to use string user IDs

-- =============================================================================
-- CHECK EXISTING TABLES AND UPDATE ONLY THOSE THAT EXIST
-- =============================================================================

-- First, let's check what tables exist and update them accordingly

-- =============================================================================
-- UPDATE USERS TABLE (if exists in public schema)
-- =============================================================================

DO $$
BEGIN
    -- Check if public.users table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
        -- Create new users table with string ID
        CREATE TABLE IF NOT EXISTS public.users_new (
            id VARCHAR(255) PRIMARY KEY,
            google_id VARCHAR(255) UNIQUE,
            email VARCHAR(255) UNIQUE NOT NULL,
            full_name VARCHAR(255),
            username VARCHAR(255) UNIQUE,
            role VARCHAR(50) DEFAULT 'user',
            status VARCHAR(50) DEFAULT 'active',
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            is_deleted BOOLEAN DEFAULT false
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_users_new_email ON public.users_new(email);
        CREATE INDEX IF NOT EXISTS idx_users_new_google_id ON public.users_new(google_id);
        CREATE INDEX IF NOT EXISTS idx_users_new_username ON public.users_new(username);

        -- Migrate existing data
        INSERT INTO public.users_new (id, email, full_name, username, role, status, is_active, created_at, updated_at, is_deleted)
        SELECT 
            id::text,
            email,
            full_name,
            username,
            role,
            status,
            is_active,
            created_at,
            updated_at,
            is_deleted
        FROM public.users
        ON CONFLICT (id) DO NOTHING;

        -- Replace old table
        DROP TABLE IF EXISTS public.users CASCADE;
        ALTER TABLE public.users_new RENAME TO users;
    ELSE
        -- Create users table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.users (
            id VARCHAR(255) PRIMARY KEY,
            google_id VARCHAR(255) UNIQUE,
            email VARCHAR(255) UNIQUE NOT NULL,
            full_name VARCHAR(255),
            username VARCHAR(255) UNIQUE,
            role VARCHAR(50) DEFAULT 'user',
            status VARCHAR(50) DEFAULT 'active',
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            is_deleted BOOLEAN DEFAULT false
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
        CREATE INDEX IF NOT EXISTS idx_users_google_id ON public.users(google_id);
        CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
    END IF;
END $$;

-- =============================================================================
-- UPDATE WALLETS TABLE (if exists)
-- =============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'wallets') THEN
        -- Drop dependent policies first
        BEGIN
            DROP POLICY IF EXISTS "Users can insert own wallet transactions" ON wallet_transactions;
            DROP POLICY IF EXISTS "Users can insert own withdrawal requests" ON withdrawal_requests;
            DROP POLICY IF EXISTS "Users can update own wallet" ON wallets;
            DROP POLICY IF EXISTS "Users can view own wallet" ON wallets;
            DROP POLICY IF EXISTS "Users can view own wallet statistics" ON wallet_statistics;
            DROP POLICY IF EXISTS "Users can view own wallet transactions" ON wallet_transactions;
            DROP POLICY IF EXISTS "Users can view own withdrawal requests" ON withdrawal_requests;
        EXCEPTION
            WHEN OTHERS THEN NULL;
        END;

        -- Update wallets table
        ALTER TABLE wallets ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
        UPDATE wallets SET user_id_new = user_id::text WHERE user_id_new IS NULL;
        ALTER TABLE wallets DROP CONSTRAINT IF EXISTS wallets_user_id_fkey;
        ALTER TABLE wallets DROP COLUMN IF EXISTS user_id CASCADE;
        ALTER TABLE wallets RENAME COLUMN user_id_new TO user_id;
        ALTER TABLE wallets ALTER COLUMN user_id SET NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);
    END IF;
END $$;

-- =============================================================================
-- UPDATE ORDERS TABLE (if exists)
-- =============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'orders') THEN
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
        UPDATE orders SET user_id_new = user_id::text WHERE user_id_new IS NULL;
        ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_user_id_fkey;
        ALTER TABLE orders DROP COLUMN IF EXISTS user_id CASCADE;
        ALTER TABLE orders RENAME COLUMN user_id_new TO user_id;
        ALTER TABLE orders ALTER COLUMN user_id SET NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
    END IF;
END $$;

-- =============================================================================
-- UPDATE PRODUCTS TABLE (if exists)
-- =============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'products') THEN
        ALTER TABLE products ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
        UPDATE products SET user_id_new = user_id::text WHERE user_id IS NOT NULL AND user_id_new IS NULL;
        ALTER TABLE products DROP CONSTRAINT IF EXISTS products_user_id_fkey;
        ALTER TABLE products DROP COLUMN IF EXISTS user_id CASCADE;
        ALTER TABLE products RENAME COLUMN user_id_new TO user_id;
        CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
    END IF;
END $$;

-- =============================================================================
-- UPDATE TRANSACTIONS TABLE (if exists)
-- =============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'transactions') THEN
        ALTER TABLE transactions ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
        UPDATE transactions SET user_id_new = user_id::text WHERE user_id_new IS NULL;
        ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_user_id_fkey;
        ALTER TABLE transactions DROP COLUMN IF EXISTS user_id CASCADE;
        ALTER TABLE transactions RENAME COLUMN user_id_new TO user_id;
        ALTER TABLE transactions ALTER COLUMN user_id SET NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
    END IF;
END $$;

-- =============================================================================
-- UPDATE FINANCIAL_OPERATIONS TABLE (if exists)
-- =============================================================================

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'financial_operations') THEN
        ALTER TABLE financial_operations ADD COLUMN IF NOT EXISTS user_id_new VARCHAR(255);
        UPDATE financial_operations SET user_id_new = user_id::text WHERE user_id_new IS NULL;
        ALTER TABLE financial_operations DROP CONSTRAINT IF EXISTS financial_operations_user_id_fkey;
        ALTER TABLE financial_operations DROP COLUMN IF EXISTS user_id CASCADE;
        ALTER TABLE financial_operations RENAME COLUMN user_id_new TO user_id;
        ALTER TABLE financial_operations ALTER COLUMN user_id SET NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_financial_operations_user_id ON financial_operations(user_id);
    END IF;
END $$;

-- =============================================================================
-- CREATE UPDATED_AT TRIGGER
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to users table
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- ADD COMMENTS
-- =============================================================================

COMMENT ON TABLE public.users IS 'Users table updated to support string IDs for Google OAuth compatibility';
COMMENT ON COLUMN public.users.id IS 'Primary key - supports both UUID strings and Google OAuth IDs';
COMMENT ON COLUMN public.users.google_id IS 'Google OAuth user ID for authentication';

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Show table information
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('users', 'wallets', 'orders', 'products', 'transactions', 'financial_operations')
    AND column_name = 'user_id'
ORDER BY table_name, column_name;
