#!/bin/bash

# =============================================================================
# CarNow Development Environment Runner
# =============================================================================
# 
# هذا الملف يقوم بتشغيل بيئة التطوير الكاملة بشكل احترافي
# يفصل بين بيئة التطوير والإنتاج تماماً
#
# الاستخدام:
#   ./run_dev.sh          # تشغيل التطبيق فقط
#   ./run_dev.sh backend  # تشغيل السيرفر فقط
#   ./run_dev.sh full     # تشغيل السيرفر والتطبيق معاً
#
# =============================================================================

set -e  # Exit on any error

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# رموز للطباعة
ROCKET="🚀"
CHECK="✅"
WARNING="⚠️"
GEAR="⚙️"
PHONE="📱"
SERVER="🖥️"

print_header() {
    echo -e "${CYAN}=================================${NC}"
    echo -e "${CYAN}🚀 CarNow Development Environment${NC}"
    echo -e "${CYAN}=================================${NC}"
}

print_step() {
    echo -e "${BLUE}${GEAR} $1${NC}"
}

print_success() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# التحقق من وجود الملفات المطلوبة
check_requirements() {
    print_step "التحقق من متطلبات التطوير..."
    
    # التحقق من Flutter
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter غير مثبت. يرجى تثبيت Flutter أولاً."
        exit 1
    fi
    
    # التحقق من Go
    if ! command -v go &> /dev/null; then
        print_error "Go غير مثبت. يرجى تثبيت Go أولاً."
        exit 1
    fi
    
    # التحقق من وجود مجلد backend-go
    if [ ! -d "backend-go" ]; then
        print_error "مجلد backend-go غير موجود."
        exit 1
    fi
    
    print_success "جميع المتطلبات متوفرة"
}

# إعداد متغيرات البيئة للتطوير
setup_dev_environment() {
    print_step "إعداد بيئة التطوير..."
    
    # متغيرات البيئة للتطوير المحلي
    export CARNOW_APP_ENVIRONMENT="development"
    export CARNOW_SERVER_HOST="0.0.0.0"
    export PORT="8080"
    export CARNOW_APP_DEBUG="true"
    
    # إعدادات قاعدة البيانات (Supabase)
    export CARNOW_DATABASE_PASSWORD="9uS2LhnExynEJNWR"
    
    # إعدادات Google OAuth
    export CARNOW_GOOGLE_CLIENT_ID="630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
    export CARNOW_GOOGLE_CLIENT_SECRET="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"
    export CARNOW_GOOGLE_ANDROID_CLIENT_ID="630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
    
    print_success "تم إعداد بيئة التطوير"
}

# تشغيل السيرفر المحلي
start_backend() {
    print_step "تشغيل السيرفر المحلي..."
    
    cd backend-go
    
    print_step "تحميل dependencies..."
    go mod tidy
    
    print_success "بدء تشغيل السيرفر على 0.0.0.0:8080"
    echo -e "${PURPLE}${SERVER} السيرفر سيكون متاحاً على:${NC}"
    echo -e "${PURPLE}  • http://localhost:8080 (للمتصفح)${NC}"
    echo -e "${PURPLE}  • http://********:8080 (للـ Android Emulator)${NC}"
    echo -e "${PURPLE}  • http://127.0.0.1:8080 (للـ iOS Simulator)${NC}"
    echo ""
    
    go run cmd/main.go
}

# تشغيل التطبيق
start_app() {
    print_step "تشغيل تطبيق Flutter..."

    # فحص سريع للـ dependencies (بدون تحديث إلا إذا لزم الأمر)
    if [ ! -f ".dart_tool/package_config.json" ]; then
        print_step "تحديث Flutter dependencies (مطلوب)..."
        flutter pub get
    else
        print_success "Dependencies محدثة مسبقاً - تخطي التحديث"
    fi

    # تخطي build_runner إلا إذا كان مطلوباً
    if [ ! -d "lib/core/providers" ] || [ ! -f "lib/core/providers/auth_provider.g.dart" ]; then
        print_step "تشغيل code generation (مطلوب)..."
        flutter packages pub run build_runner build --delete-conflicting-outputs
    else
        print_success "Code generation محدث مسبقاً - تخطي التوليد"
    fi

    print_success "بدء تشغيل التطبيق - سريع!"
    echo -e "${PURPLE}${PHONE} التطبيق سيتصل بالسيرفر المحلي تلقائياً${NC}"
    echo -e "${YELLOW}💡 نصيحة: للتحديث الإجباري استخدم: flutter pub get && flutter packages pub run build_runner build${NC}"
    echo ""

    # تشغيل Flutter مع الإعدادات المحلية
    flutter run \
        --dart-define=API_BASE_URL=http://********:8080 \
        --dart-define=FLUTTER_ENV=development \
        --dart-define=ENABLE_DEBUG_LOGGING=true
}

# تشغيل السيرفر والتطبيق معاً
start_full_environment() {
    print_step "تشغيل البيئة الكاملة..."
    
    # تشغيل السيرفر في الخلفية
    print_step "تشغيل السيرفر في الخلفية..."
    cd backend-go
    go mod tidy
    nohup go run cmd/main.go > ../backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # انتظار حتى يبدأ السيرفر
    print_step "انتظار بدء السيرفر..."
    sleep 5
    
    # التحقق من أن السيرفر يعمل
    if curl -s http://localhost:8080/health > /dev/null; then
        print_success "السيرفر يعمل بنجاح"
    else
        print_error "فشل في تشغيل السيرفر"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    
    # تشغيل التطبيق
    start_app
    
    # إيقاف السيرفر عند إنهاء التطبيق
    print_step "إيقاف السيرفر..."
    kill $BACKEND_PID 2>/dev/null || true
    print_success "تم إيقاف البيئة بنجاح"
}

# تشغيل التطبيق مع التحديث الإجباري
start_app_fresh() {
    print_step "تشغيل تطبيق Flutter مع التحديث الإجباري..."

    print_step "تحديث Flutter dependencies..."
    flutter pub get

    print_step "تشغيل code generation..."
    flutter packages pub run build_runner build --delete-conflicting-outputs

    print_success "بدء تشغيل التطبيق - محدث بالكامل!"
    echo -e "${PURPLE}${PHONE} التطبيق سيتصل بالسيرفر المحلي تلقائياً${NC}"
    echo ""

    # تشغيل Flutter مع الإعدادات المحلية
    flutter run \
        --dart-define=API_BASE_URL=http://********:8080 \
        --dart-define=FLUTTER_ENV=development \
        --dart-define=ENABLE_DEBUG_LOGGING=true
}

# عرض المساعدة
show_help() {
    echo -e "${CYAN}الاستخدام:${NC}"
    echo -e "  ${GREEN}./run_dev.sh${NC}          تشغيل السيرفر المحلي فقط (الافتراضي)"
    echo -e "  ${GREEN}./run_dev.sh backend${NC}  تشغيل السيرفر فقط"
    echo -e "  ${GREEN}./run_dev.sh app${NC}      تشغيل التطبيق فقط (سريع - بدون تحديث)"
    echo -e "  ${GREEN}./run_dev.sh fresh${NC}    تشغيل التطبيق مع التحديث الإجباري"
    echo -e "  ${GREEN}./run_dev.sh full${NC}     تشغيل السيرفر والتطبيق معاً"
    echo -e "  ${GREEN}./run_dev.sh help${NC}     عرض هذه المساعدة"
    echo ""
    echo -e "${CYAN}ملاحظات:${NC}"
    echo -e "  • السيرفر يعمل على ${YELLOW}0.0.0.0:8080${NC} ليكون متاحاً للـ emulator"
    echo -e "  • ${GREEN}app${NC} = تشغيل سريع بدون تحديث dependencies"
    echo -e "  • ${GREEN}fresh${NC} = تشغيل مع تحديث كامل للـ dependencies"
    echo -e "  • جميع إعدادات التطوير منفصلة عن الإنتاج"
}

# الدالة الرئيسية
main() {
    print_header
    
    # إعداد بيئة التطوير
    setup_dev_environment
    
    # التحقق من المتطلبات
    check_requirements
    
    # تحديد نوع التشغيل - الافتراضي هو السيرفر فقط
    case "${1:-backend}" in
        "backend")
            start_backend
            ;;
        "app")
            start_app
            ;;
        "fresh")
            start_app_fresh
            ;;
        "full")
            start_full_environment
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "خيار غير صحيح: $1"
            show_help
            exit 1
            ;;
    esac
}

# تشغيل الدالة الرئيسية
main "$@"
