# 🚀 CarNow Development Guide

## 🏗️ بيئات التطوير البسيطة

نظام بسيط وواضح لفصل التطوير عن الإنتاج.

## 📋 البيئات المتاحة

### 🔧 **Development (التطوير)**
- **الغرض**: التطوير المحلي والاختبار
- **Backend**: محلي على `localhost:8080`
- **Flutter**: يتصل بـ `http://********:8080`
- **المميزات**: Hot reload، Debug logging، CORS enabled

### 🚀 **Production (الإنتاج)**
- **الغرض**: النشر النهائي
- **Backend**: `https://backend-go-8klm.onrender.com` (الاستضافة)
- **Flutter**: يتصل تلقائياً بالاستضافة
- **المميزات**: Optimized، Security enhanced

## 🛠️ كيفية الاستخدام

### **1️⃣ للتطوير المحلي**

```bash
# تشغيل Backend محلي
cd backend-go && ./run-dev.sh

# في terminal آخر - تشغيل Flutter للتطوير
./run-dev.sh
```

### **2️⃣ للإنتاج**

```bash
# تشغيل Flutter عادي (يتصل بالاستضافة تلقائياً)
flutter run --release
```

## 🔧 إعدادات البيئات

### **Development (التطوير)**

```bash
# Backend محلي
CARNOW_APP_ENVIRONMENT=development
CARNOW_APP_DEBUG=true
CARNOW_SERVER_HOST=0.0.0.0
PORT=8080

# Flutter
API_BASE_URL=http://********:8080  # يتصل بالـ backend المحلي
```

### **Production (الإنتاج)**

```bash
# Flutter عادي - يكتشف الاستضافة تلقائياً
# لا حاجة لمتغيرات إضافية
```

## 📱 اختبار الاتصال

### **فحص Backend المحلي:**
```bash
curl http://localhost:8080/health
```

### **فحص Backend الإنتاج:**
```bash
curl https://backend-go-8klm.onrender.com/health
```

### **فحص من Android Emulator:**
```bash
# من داخل الـ emulator
curl http://********:8080/health
```

## 🎯 مميزات النظام الجديد

### ✅ **فصل كامل للبيئات**
- تطوير محلي آمن
- اختبار منفصل
- إنتاج محمي

### ✅ **سهولة التبديل**
- ملف واحد لكل بيئة
- لا تضارب في الإعدادات
- تشغيل بأمر واحد

### ✅ **احترافية عالية**
- متغيرات بيئة منظمة
- validation للإعدادات
- logging مناسب لكل بيئة

### ✅ **أمان محسن**
- إعدادات تطوير منفصلة
- أسرار إنتاج محمية
- CORS محكوم حسب البيئة

## 🚨 تحذيرات مهمة

### ⚠️ **لا تخلط البيئات**
- استخدم `run-dev.sh` للتطوير فقط
- استخدم `run-prod.sh` للإنتاج فقط

### ⚠️ **أسرار الإنتاج**
- لا تضع أسرار الإنتاج في ملفات التطوير
- استخدم `.env` للأسرار الحساسة

### ⚠️ **اختبار قبل النشر**
- اختبر دائماً في `staging` قبل `production`
- تأكد من عمل جميع APIs

## 🔄 سير العمل البسيط

1. **التطوير**: `cd backend-go && ./run-dev.sh` + `./run-dev.sh`
2. **الإنتاج**: `flutter run --release` (يتصل بالاستضافة تلقائياً)

## 📞 المساعدة

إذا واجهت مشاكل:

1. **تأكد من تشغيل Backend أولاً**
2. **تحقق من الـ ports (8080)**
3. **راجع logs الخاصة بكل بيئة**
4. **استخدم `curl` لاختبار الاتصال**
