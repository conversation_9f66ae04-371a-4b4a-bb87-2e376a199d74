# 🚀 CarNow - Quick Start للتطوير

## 🔧 تشغيل التطوير المحلي

### **الطريقة الأولى: استخدام الملفات المخصصة (الموصى بها)**

```bash
# 1. تشغيل السيرفر المحلي
./run_dev.sh

# 2. في terminal آخر - تشغيل التطبيق (سريع)
./run_dev.sh app        # ← سريع! بدون تحديث dependencies

# أو للتحديث الكامل (إذا أضفت مكتبات جديدة)
./run_dev.sh fresh      # ← مع تحديث dependencies
```

### **الطريقة الثانية: VS Code**

1. **تشغيل السيرفر المحلي:**
   ```bash
   ./run_dev.sh
   ```

2. **تشغيل التطبيق من VS Code:**
   - اضغط `F5` أو `Ctrl+F5`
   - اختر `CarNow - Development (Local Backend)`

### **الطريقة الثالثة: سطر الأوامر**

```bash
# 1. تشغيل السيرفر المحلي
./run_dev.sh

# 2. تشغيل التطبيق مع الإعدادات الصحيحة
flutter run \
  --dart-define=API_BASE_URL=http://********:8080 \
  --dart-define=FLUTTER_ENV=development \
  --dart-define=ENABLE_DEBUG_LOGGING=true
```

## ✅ **التأكد من الاتصال الصحيح**

عند تشغيل التطبيق بالطريقة الصحيحة، ستظهر هذه الرسالة:

```
🔧 Using API_BASE_URL from environment: http://********:8080
```

## ❌ **إذا ظهرت هذه الرسالة:**

```
✅ Using hosted server permanently: https://backend-go-8klm.onrender.com
```

**معناها أن التطبيق لم يتم تشغيله بالإعدادات الصحيحة!**

## 🚀 **للإنتاج:**

```bash
flutter run --release
# سيتصل بالاستضافة تلقائياً
```

## 📋 **الملفات المهمة:**

- `./run_dev.sh` - تشغيل السيرفر المحلي
- `.vscode/launch.json` - إعدادات VS Code
- `.vscode/settings.json` - إعدادات VS Code الافتراضية

## 🚀 **خيارات التشغيل:**

- `./run_dev.sh app` - **سريع!** بدون تحديث dependencies
- `./run_dev.sh fresh` - مع تحديث كامل للـ dependencies
- `./run_dev.sh full` - تشغيل السيرفر والتطبيق معاً

## 🔧 **استكشاف الأخطاء:**

1. **تأكد من تشغيل السيرفر المحلي أولاً:** `./run_dev.sh`
2. **تأكد من ظهور رسالة:** `🔧 Using API_BASE_URL from environment`
3. **إذا لم تظهر الرسالة، استخدم:** `./run_dev.sh app`
4. **إذا كان التطبيق بطيء، استخدم:** `./run_dev.sh app` (سريع)
5. **إذا أضفت مكتبات جديدة، استخدم:** `./run_dev.sh fresh`
